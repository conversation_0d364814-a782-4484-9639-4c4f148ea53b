{"ethereum": {"chainId": 1, "chainSymbol": "ETH", "chainDisplayName": "Ethereum", "chainDecimals": 18, "explorerUrl": "https://etherscan.io", "rpcEndpoint": "https://mainnet.infura.io/v3/", "dex": {"uniswapV2": {"router": "******************************************", "factory": "******************************************", "weth": "******************************************"}}, "abi": ["function swapExactTokensForTokens(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts)", "function swapExactETHForTokens(uint amountOutMin, address[] calldata path, address to, uint deadline) external payable returns (uint[] memory amounts)", "function swapExactTokensForETH(uint amountIn, uint amountOutMin, address[] calldata path, address to, uint deadline) external returns (uint[] memory amounts)", "function getAmountsOut(uint amountIn, address[] calldata path) external view returns (uint[] memory amounts)", "function approve(address spender, uint256 amount) external returns (bool)", "function allowance(address owner, address spender) external view returns (uint256)", "function balanceOf(address account) external view returns (uint256)", "function decimals() external view returns (uint8)", "function transfer(address to, uint256 amount) external returns (bool)", "function transferFrom(address from, address to, uint256 amount) external returns (bool)", "function symbol() external view returns (string memory)", "function name() external view returns (string memory)"]}, "solana": {"chainId": 101, "chainSymbol": "SOL", "chainDisplayName": "Solana", "chainDecimals": 9, "explorerUrl": "https://explorer.solana.com", "rpcEndpoint": "https://api.mainnet-beta.solana.com", "dex": {"jupiter": {"programId": "JUP6LkbZbjS1jKKwapdHNy74zcZ3tLUZoi5QNyVTaV4", "apiUrl": "https://quote-api.jup.ag/v6"}}, "abi": []}}