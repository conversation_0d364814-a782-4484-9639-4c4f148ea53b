<b>⚙️ Your All Configurations</b>

<b>Total Config:</b> {{totalConfig}}
<b>Page:</b> {{currentPage}} of {{totalPages}}

<render>
    let str = ""
    const len = configList.length
    for (let index = 0; index < len; index++) {
        const config = configList[index]
        str += `${startIndex + index + 1}. ${config.name}\n🚦 status: ${config.status} ${(index === len - 1 ? `\n`: `\n\n`)}`
    }
    return str
</render>

<b>💡 Quick Actions:</b>
• Use <b>Create</b> to add new config
• Navigate with pagination buttons if you have many config

Select a config below to view details: