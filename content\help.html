<b>🤖 Trading Bot Help</b>

Welcome to the Trading Bot! This bot uses an intuitive button-based interface for all operations.

<b>🚀 Getting Started:</b>
• Send any message to access the main menu
• Use the buttons to navigate through different sections
• All operations are performed through interactive menus

<b>💼 Wallet Management:</b>
• <b>My Wallets</b> - View all your wallets with balances and details
• <b>Create Wallet</b> - Create new wallets on supported networks
• <b>Import Wallet</b> - Import existing wallets using private keys
• <b>Wallet Details</b> - View balance, history, and statistics for each wallet
• <b>Delete Wallet</b> - Remove wallets with confirmation prompts

<b>📊 Trading & Statistics:</b>
• <b>View Stats</b> - See comprehensive trading analytics and performance
• <b>Wallet History</b> - Check trading history for individual wallets
• <b>Wallet Stats</b> - View detailed statistics for individual wallets
• <b>Success Rates</b> - Monitor trading performance metrics

<b>⚙️ Configuration:</b>
• <b>Settings Menu</b> - Access all configuration options
• <b>Fee Settings</b> - Configure trading fees and preferences
• <b>Chain Settings</b> - Manage blockchain network settings
• <b>Language</b> - Set your preferred language (English currently supported)
• <b>Notifications</b> - Configure notification preferences

<b>🔗 Supported Networks:</b>
• Solana
• Ethereum
• More networks coming soon!

<b>💡 Tips:</b>
• Wallet names must be unique per user
• Keep your private keys secure - never share them
• Check network fees before trading
• Use the back buttons to navigate between menus
• All sensitive operations require confirmation

<b>🔒 Security:</b>
• Private keys are securely encrypted and stored
• All wallet operations require user confirmation
• Regular security audits ensure data protection

Need more help? Use the help sections in the main menu for specific topics!
