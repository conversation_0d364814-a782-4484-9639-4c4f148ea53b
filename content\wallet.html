<b>💼 Your Wallets</b>

<b>Total Wallets:</b> {{totalWallets}}
<b>Page:</b> {{currentPage}} of {{totalPages}}

<render>
    let str = ""
    const len = walletList.length
    for (let index = 0; index < len; index++) {
        const wallet = walletList[index]
        str += `${startIndex + index + 1}. ${wallet.name}\n🔗 ${wallet.chain}\n💰 ${wallet.balance} (${wallet.chainSymbol})\n📍 <code>${wallet.address}</code> ${(index === len - 1 ? `\n`: `\n\n`)}`
    }
    return str
</render>

<b>💡 Quick Actions:</b>
• Use <b>Create</b> to add new wallets
• Use <b>Import</b> to add existing wallets
• Navigate with pagination buttons if you have many wallets

Select a wallet below to view details: