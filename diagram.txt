graph TD
    A["🔧 SETUP BOT CONFIGURATION<br/>INSERT INTO config<br/>(rebuyBps=1000, sellBps=2000, maxPendingSell=15)"] --> B["📊 INITIALIZE MONITORING<br/>INSERT INTO monitor<br/>(pendingSell=0, status='open')"]

    B --> C["💰 EXECUTE FIRST PURCHASE<br/>INSERT INTO tx<br/>(type='buy', amountIn=1SOL, status='success')"]

    C --> D["📈 UPDATE PORTFOLIO TOTALS<br/>UPDATE monitor SET<br/>totalAmountIn=1SOL, totalAmountOut=100tokens"]

    D --> E["🎯 CREATE SELL ORDER (+20% PROFIT)<br/>INSERT INTO tx<br/>(type='sell', target=+20%, status='pending')"]

    E --> F["📊 INCREMENT PENDING SELL COUNTER<br/>UPDATE monitor SET pendingSell=1"]

    F --> G["🔄 CREATE REBUY ORDERS (±10%)<br/>INSERT INTO tx VALUES<br/>(type='buy_up', +10% price, status='pending'),<br/>(type='buy_down', -10% price, status='pending')"]

    G --> H{"🔍 CHECK FOR EXECUTED REBUYS<br/>SELECT * FROM tx<br/>WHERE status='pending'<br/>AND type IN ('buy_up','buy_down')"}

    H -->|"One rebuy executed"| I["✅ MARK REBUY AS SUCCESSFUL<br/>UPDATE tx SET status='success'<br/>WHERE type='buy_down'"]

    I --> J["❌ CANCEL UNFILLED REBUY ORDER<br/>UPDATE tx SET status='failed'<br/>WHERE type='buy_up'"]

    J --> K["📈 UPDATE PORTFOLIO WITH NEW PURCHASE<br/>UPDATE monitor SET<br/>totalAmountIn += newAmount,<br/>totalAmountOut += newTokens"]

    K --> L["🎯 CREATE SELL ORDER FOR NEW TOKENS<br/>INSERT INTO tx<br/>(type='sell', target=+20%, status='pending')"]

    L --> M["📊 INCREMENT PENDING SELL COUNTER<br/>UPDATE monitor SET pendingSell += 1"]

    M --> N["🔄 CREATE NEW REBUY ORDERS (±10%)<br/>INSERT INTO tx VALUES<br/>(type='buy_up', +10%, status='pending'),<br/>(type='buy_down', -10%, status='pending')"]

    N --> O{"⚠️ CHECK PENDING SELL LIMIT<br/>SELECT pendingSell, maxPendingSell<br/>FROM monitor<br/>WHERE pendingSell >= maxPendingSell"}

    O -->|"pendingSell < maxPendingSell"| H

    O -->|"pendingSell >= maxPendingSell"| P["⏸️ PAUSE BOT (LIMIT REACHED)<br/>UPDATE config<br/>SET status='paused'"]

    P --> Q{"🔍 MONITOR FOR SELL COMPLETIONS<br/>SELECT pendingSell FROM monitor<br/>WHERE pendingSell < maxPendingSell"}

    Q -->|"Still at limit"| P
    Q -->|"Below limit"| R["▶️ RESUME BOT OPERATIONS<br/>UPDATE config<br/>SET status='active'"]

    R --> H

    H -->|"No rebuy executed"| S["⏳ WAIT FOR MARKET MOVEMENT<br/>SELECT * FROM tx<br/>WHERE status='pending'<br/>(Wait for market movement)"]

    S --> H

    T["💸 SELL ORDER COMPLETED<br/>UPDATE tx SET status='success'<br/>WHERE type='sell'<br/>(When sell completes)"] --> U["📊 DECREMENT PENDING SELL COUNTER<br/>UPDATE monitor SET<br/>pendingSell -= 1"]

    U --> H

    style A fill:#e1f5fe
    style C fill:#c8e6c9
    style E fill:#fff3e0
    style P fill:#ffebee
    style H fill:#f3e5f5
    style O fill:#f3e5f5
    style Q fill:#f3e5f5