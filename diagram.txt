graph TD
    A["INSERT INTO config<br/>(rebuyBps=1000, sellBps=2000, maxPendingSell=15)"] --> B["INSERT INTO monitor<br/>(pendingSell=0, status='open')"]

    B --> C["INSERT INTO tx<br/>(type='buy', amountIn=1SOL, status='success')"]

    C --> D["UPDATE monitor SET<br/>totalAmountIn=1SOL, totalAmountOut=100tokens"]

    D --> E["INSERT INTO tx<br/>(type='sell', target=+20%, status='pending')"]

    E --> F["UPDATE monitor SET pendingSell=1"]

    F --> G["INSERT INTO tx VALUES<br/>(type='buy_up', +10% price, status='pending'),<br/>(type='buy_down', -10% price, status='pending')"]

    G --> H{"SELECT * FROM tx<br/>WHERE status='pending'<br/>AND type IN ('buy_up','buy_down')"}

    H -->|"One rebuy executed"| I["UPDATE tx SET status='success'<br/>WHERE type='buy_down'"]

    I --> J["UPDATE tx SET status='failed'<br/>WHERE type='buy_up'"]

    J --> K["UPDATE monitor SET<br/>totalAmountIn += newAmount,<br/>totalAmountOut += newTokens"]

    K --> L["INSERT INTO tx<br/>(type='sell', target=+20%, status='pending')"]

    L --> M["UPDATE monitor SET pendingSell += 1"]

    M --> N["INSERT INTO tx VALUES<br/>(type='buy_up', +10%, status='pending'),<br/>(type='buy_down', -10%, status='pending')"]

    N --> O{"SELECT pendingSell, maxPendingSell<br/>FROM monitor<br/>WHERE pendingSell >= maxPendingSell"}

    O -->|"pendingSell < maxPendingSell"| H

    O -->|"pendingSell >= maxPendingSell"| P["UPDATE config<br/>SET status='paused'"]

    P --> Q{"SELECT pendingSell FROM monitor<br/>WHERE pendingSell < maxPendingSell"}

    Q -->|"Still at limit"| P
    Q -->|"Below limit"| R["UPDATE config<br/>SET status='active'"]

    R --> H

    H -->|"No rebuy executed"| S["SELECT * FROM tx<br/>WHERE status='pending'<br/>(Wait for market movement)"]

    S --> H

    T["UPDATE tx SET status='success'<br/>WHERE type='sell'<br/>(When sell completes)"] --> U["UPDATE monitor SET<br/>pendingSell -= 1"]

    U --> H

    style A fill:#e1f5fe
    style C fill:#c8e6c9
    style E fill:#fff3e0
    style P fill:#ffebee
    style H fill:#f3e5f5
    style O fill:#f3e5f5
    style Q fill:#f3e5f5