-- TRADING BOT FLOW - <PERSON>QL EXAMPLES

-- 1. SETUP: Create bot configuration
INSERT INTO config (id, walletId, name, initAmountIn, tokenIn, tokenOut, rebuyBps, sellBps, maxPendingSell, status)
VALUES ('config-001', 'wallet-123', 'SOL-MEME Bot', 1000000000, 'So11111111111111111111111111111111111111112', 'TokenABC123...', 1000, 2000, 15, 'active');

-- 2. INITIALIZE: Create monitor record
INSERT INTO monitor (id, configId, maxPendingSell, status, pendingSell, totalAmountIn, totalAmountOut)
VALUES (1, 'config-001', 15, 'open', 0, 0, 0);

-- 3. FIRST PURCHASE: <PERSON><PERSON> buys tokens worth 1 SOL
INSERT INTO tx (id, configId, chain, hash, fromAddr, toAddr, type, amountIn, amountOut, slippage, tokenIn, tokenOut, status)
VALUES (1, 'config-001', 'solana', 'hash001', 'wallet-123', 'dex-address', 'buy', 1000000000, 100000000000, 50, 'So11111111111111111111111111111111111111112', 'TokenABC123...', 'success');

-- 4. UPDATE MONITOR: Track first purchase
UPDATE monitor SET totalAmountIn = 1000000000, totalAmountOut = 100000000000 WHERE configId = 'config-001';

-- 5. CREATE SELL ORDER: Target +20% price increase
INSERT INTO tx (id, configId, chain, hash, fromAddr, toAddr, type, amountIn, amountOut, slippage, tokenIn, tokenOut, status)
VALUES (2, 'config-001', 'solana', 'pending-sell-001', 'wallet-123', 'dex-address', 'sell', 100000000000, 1200000000, 50, 'TokenABC123...', 'So11111111111111111111111111111111111111112', 'pending');

-- 6. UPDATE MONITOR: Increment pending sell count
UPDATE monitor SET pendingSell = 1 WHERE configId = 'config-001';

-- 7. CREATE REBUY ORDERS: +10% and -10% from last purchase
INSERT INTO tx (id, configId, chain, hash, fromAddr, toAddr, type, amountIn, amountOut, slippage, tokenIn, tokenOut, status)
VALUES
(3, 'config-001', 'solana', 'pending-rebuy-up-001', 'wallet-123', 'dex-address', 'buy_up', 1100000000, 90909090909, 50, 'So11111111111111111111111111111111111111112', 'TokenABC123...', 'pending'),
(4, 'config-001', 'solana', 'pending-rebuy-down-001', 'wallet-123', 'dex-address', 'buy_down', 900000000, 111111111111, 50, 'So11111111111111111111111111111111111111112', 'TokenABC123...', 'pending');

-- 8. REBUY EXECUTED: One rebuy order fills (buy_down executed)
UPDATE tx SET status = 'success', hash = 'hash002' WHERE id = 4;

-- 9. CANCEL OTHER REBUY: Cancel the unfilled rebuy order
UPDATE tx SET status = 'failed' WHERE id = 3;

-- 10. UPDATE MONITOR: Track new purchase
UPDATE monitor SET totalAmountIn = totalAmountIn + 900000000, totalAmountOut = totalAmountOut + 111111111111 WHERE configId = 'config-001';

-- 11. CREATE NEW SELL ORDER: For newly purchased tokens
INSERT INTO tx (id, configId, chain, hash, fromAddr, toAddr, type, amountIn, amountOut, slippage, tokenIn, tokenOut, status)
VALUES (5, 'config-001', 'solana', 'pending-sell-002', 'wallet-123', 'dex-address', 'sell', 111111111111, 1080000000, 50, 'TokenABC123...', 'So11111111111111111111111111111111111111112', 'pending');

-- 12. UPDATE MONITOR: Increment pending sell count
UPDATE monitor SET pendingSell = 2 WHERE configId = 'config-001';

-- 13. CREATE NEW REBUY ORDERS: ±10% from current price
INSERT INTO tx (id, configId, chain, hash, fromAddr, toAddr, type, amountIn, amountOut, slippage, tokenIn, tokenOut, status)
VALUES
(6, 'config-001', 'solana', 'pending-rebuy-up-002', 'wallet-123', 'dex-address', 'buy_up', 990000000, 100000000000, 50, 'So11111111111111111111111111111111111111112', 'TokenABC123...', 'pending'),
(7, 'config-001', 'solana', 'pending-rebuy-down-002', 'wallet-123', 'dex-address', 'buy_down', 810000000, 122222222222, 50, 'So11111111111111111111111111111111111111112', 'TokenABC123...', 'pending');

-- 14. CHECK MAX PENDING SELL LIMIT
SELECT
    m.pendingSell,
    m.maxPendingSell,
    CASE WHEN m.pendingSell >= m.maxPendingSell THEN 'PAUSE_BOT' ELSE 'CONTINUE' END as bot_action
FROM monitor m
WHERE m.configId = 'config-001';

-- 15. WHEN SELL ORDER COMPLETES: Update transaction and monitor
UPDATE tx SET status = 'success', hash = 'hash003' WHERE id = 2;
UPDATE monitor SET pendingSell = pendingSell - 1 WHERE configId = 'config-001';

-- 16. QUERY CURRENT BOT STATUS
SELECT
    c.name,
    c.status as config_status,
    m.status as monitor_status,
    m.pendingSell,
    m.maxPendingSell,
    m.totalAmountIn,
    m.totalAmountOut,
    COUNT(CASE WHEN t.status = 'pending' AND t.type IN ('buy_up', 'buy_down') THEN 1 END) as pending_rebuys,
    COUNT(CASE WHEN t.status = 'pending' AND t.type = 'sell' THEN 1 END) as pending_sells
FROM config c
JOIN monitor m ON c.id = m.configId
LEFT JOIN tx t ON c.id = t.configId
WHERE c.id = 'config-001'
GROUP BY c.id, c.name, c.status, m.status, m.pendingSell, m.maxPendingSell, m.totalAmountIn, m.totalAmountOut;

-- 17. PAUSE BOT WHEN MAX PENDING REACHED
UPDATE config SET status = 'paused' WHERE id = 'config-001' AND (
    SELECT pendingSell FROM monitor WHERE configId = 'config-001'
) >= (
    SELECT maxPendingSell FROM monitor WHERE configId = 'config-001'
);

-- 18. RESUME BOT WHEN PENDING SELLS REDUCE
UPDATE config SET status = 'active' WHERE id = 'config-001' AND status = 'paused' AND (
    SELECT pendingSell FROM monitor WHERE configId = 'config-001'
) < (
    SELECT maxPendingSell FROM monitor WHERE configId = 'config-001'
);