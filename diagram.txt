graph TD
    A[Start: Input Token Address] --> B[Set Initial Purchase Amount<br/>Example: 1 SOL]
    B --> C[Configure Parameters<br/>Rebuy: 10%<br/>Sell: 20%]
    C --> D[Set Max Pending Sell Limit<br/>Example: 15 transactions]
    D --> E[Press OK to Start]
    E --> F[Bot Makes First Purchase<br/>Buy tokens worth 1 SOL<br/>Result: 100 tokens]
    F --> G[Create First Sell Order<br/>Target: +20% price increase]
    G --> H[Create Two Rebuy Orders<br/>1. +10% from last purchase<br/>2. -10% from last purchase]
    H --> I{One Rebuy Order Executed?}
    I -->|Yes| J[Cancel Other Rebuy Order]
    J --> K[Create Sell Order for New Tokens<br/>Target: +20% increase]
    K --> L[Create Two New Rebuy Orders<br/>±10% from current price]
    L --> M{Max Pending Sell Reached?}
    M -->|No| I
    M -->|Yes| N[Bot Pauses<br/>Wait for sells to complete]
    N --> O{Pending Sells Reduced?}
    O -->|Yes| I
    O -->|No| N
    I -->|No| P[Wait for Market Movement]
    P --> I

    subgraph "Database Schema"
        Q[config Table<br/>- id: config ID<br/>- walletId: owner wallet<br/>- name: config name<br/>- initAmountIn: initial amount<br/>- tokenIn/Out: token addresses<br/>- rebuyBps: rebuy %<br/>- sellBps: sell %<br/>- maxPendingSell: max pending<br/>- status: active/inactive]

        R[monitor Table<br/>- id: increment ID<br/>- configId: references config<br/>- maxPendingSell: max limit<br/>- status: open/close<br/>- pendingSell: current count<br/>- totalAmountIn/Out: totals]

        S[tx Table<br/>- id: increment ID<br/>- configId: references config<br/>- chain: blockchain name<br/>- hash: transaction hash<br/>- from/to: addresses<br/>- type: buy/sell/buy_up/buy_down<br/>- amountIn/Out: token amounts<br/>- slippage: basis points<br/>- tokenIn/Out: token addresses<br/>- status: pending/success/failed]

        Q -->|1:1| R
        Q -->|1:N| S
    end

    style A fill:#e1f5fe
    style F fill:#c8e6c9
    style G fill:#fff3e0
    style N fill:#ffebee
    style Q fill:#f3e5f5
    style R fill:#e8f5e8
    style S fill:#fff8e1