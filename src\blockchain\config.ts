import config from "../../config.json"

export class BlockchainConfig {
  static get<K extends keyof typeof config>(chainName: K): (typeof config)[K] {
    const target = config[chainName]
    if (!target) {
      throw new Error(`Unsupported chain: ${String(chainName)}`)
    }

    return new Proxy(target, {
      get(_, prop, receiver) {
        return Reflect.get(target, prop, receiver)
      },
      set(_, prop, value, receiver) {
        return Reflect.set(target, prop, value, receiver)
      }
    }) as (typeof config)[K]
  }

  static listChainName = Object.keys(config)
  static listChainDisplayName = this.listChainName.map((it) => (config as any)[it].chainDisplayName)
  static listChainSymbol = this.listChainName.map((it) => (config as any)[it].chainSymbol)
  static listChainId = this.listChainName.map((it) => (config as any)[it].chainId)
}
