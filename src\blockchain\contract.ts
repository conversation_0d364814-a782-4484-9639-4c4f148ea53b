import { BlockchainEthereumContract } from "./ethereum/contract"
import { BlockchainSolanaContract } from "./solana/contract"

const MAP = {
  ethereum: new BlockchainEthereumContract(),
  solana: new BlockchainSolanaContract()
}

export class BlockchainContract {
  static get(chainName: string) {
    const target = MAP[chainName as keyof typeof MAP]
    if (!target) {
      throw new Error(`Unsupported chain: ${chainName}`)
    }

    // Proxy untuk auto-forward semua properti/method ke target
    return new Proxy(target, {
      get(_, prop, receiver) {
        return Reflect.get(target, prop, receiver)
      },
      set(_, prop, value, receiver) {
        return Reflect.set(target, prop, value, receiver)
      }
    })
  }
}
