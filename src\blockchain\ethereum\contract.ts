import { JsonRp<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Contract } from "ethers"
import { BlockchainEthereumProvider } from "./provider"
import { BlockchainConfig } from "../config"
import { BlockchainUtils } from "../utils"
import { log } from "../../utils/log"

type AmountResult = { result?: bigint | string; error?: string }
type TokenInfo = { address: string; decimals: number; symbol?: string; name?: string }
type SwapParams = { fromToken: string; toToken: string; amount: string; slippage?: number; deadline?: number }
type SwapResult = { txHash?: string; error?: string; amountOut?: string }
const { rpcEndpoint, dex, abi } = BlockchainConfig.get(`ethereum`)

export class BlockchainEthereumContract {
  private name = `BlockchainEthereumContract`
  private provider = new BlockchainEthereumProvider()
  constructor() {}

  async tokenBalance(tokenAddress: string, walletAddress: string, format = false): Promise<AmountResult> {
    try {
      const tokenContract = new Contract(tokenAddress, abi, this.provider._real)
      let result = await (tokenContract.balanceOf as any)(walletAddress)
      if (format === true) {
        const decimals = await (tokenContract.decimals as any)()
        result = BlockchainUtils.formatUnits(result, decimals)
      }
      return { result }
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:tokenBalance: ${e}`)
      return { error: e }
    }
  }

  async tokenTransfer(privateKey: string, tokenAddress: string, toAddress: string, amount: bigint): Promise<AmountResult> {
    try {
      const wallet = new Wallet(privateKey, this.provider._real)
      const tokenContract = new Contract(tokenAddress, abi, wallet)
      const tx = await (tokenContract.transfer as any)(toAddress, amount)
      const receipt = await tx.wait()
      if (receipt?.status !== 1) {
        throw `status tx not 1`
      }
      return { result: tx.hash }
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:tokenTransfer: ${e}`)
      return { error: e }
    }
  }

  async tokenInfo(tokenAddress: string): Promise<TokenInfo | { error: string }> {
    try {
      const tokenContract = new Contract(tokenAddress, abi, this.provider._real)
      const decimals = await (tokenContract.decimals as any)()

      // Try to get symbol and name (optional)
      let symbol, name
      try {
        symbol = await (tokenContract.symbol as any)()
      } catch {
        symbol = `Unknown`
      }

      try {
        name = await (tokenContract.name as any)()
      } catch {
        name = `Unknown Token`
      }

      return {
        address: tokenAddress,
        decimals,
        symbol,
        name
      }
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:tokenInfo: ${e}`)
      return { error: e }
    }
  }

  async tokenApprove(privateKey: string, tokenAddress: string, spenderAddress: string, amount: string = `max`): Promise<AmountResult> {
    try {
      const wallet = new Wallet(privateKey, this.provider._real)
      const tokenContract = new Contract(tokenAddress, abi, wallet)

      let amountApprove = BlockchainUtils.maxUint256
      if (amount !== `max`) {
        const decimals = await (tokenContract.decimals as any)()
        amountApprove = BlockchainUtils.parseUnits(amount, decimals)
      }
      const tx = await (tokenContract.approve as any)(spenderAddress, amountApprove)
      const receipt = await tx.wait()
      if (receipt?.status !== 1) {
        throw `status tx not 1`
      }
      return { result: tx.hash }
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:tokenApprove: ${e}`)
      return { error: e }
    }
  }

  async tokenAllowance(tokenAddress: string, ownerAddress: string, spenderAddress: string, format = false): Promise<AmountResult> {
    try {
      const tokenContract = new Contract(tokenAddress, abi, this.provider._real)
      let result = await (tokenContract.allowance as any)(ownerAddress, spenderAddress)
      if (format === true) {
        const decimals = await (tokenContract.decimals as any)()
        result = BlockchainUtils.formatUnits(result, decimals)
      }
      return { result }
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:tokenAllowance: ${e}`)
      return { error: e }
    }
  }

  async swapToken(privateKey: string, params: SwapParams): Promise<SwapResult> {
    try {
      const routerAddress = dex.uniswapV2.router
      const wethAddress = dex.uniswapV2.weth
      const provider = new JsonRpcProvider(rpcEndpoint)
      const wallet = new Wallet(privateKey, provider)
      const router = new Contract(routerAddress, abi, wallet)

      const isETHToToken = params.fromToken.toLowerCase() === `eth`
      const isTokenToETH = params.toToken.toLowerCase() === `eth`
      let path: string[]
      if (isETHToToken) {
        path = [wethAddress, params.toToken]
      } else if (isTokenToETH) {
        path = [params.fromToken, wethAddress]
      } else {
        path = [params.fromToken, wethAddress, params.toToken]
      }

      const slippage = params.slippage || 3 // 3% default slippage
      const deadline = params.deadline || Math.floor(Date.now() / 1000) + 600 // 10 minutes
      if (isETHToToken) {
        // ETH to Token swap
        const amountIn = params.amount
        const amounts = await (router.getAmountsOut as any)(amountIn, path)
        const amountOutMin = (amounts[1] * BigInt(100 - slippage)) / 100n
        const tx = await (router.swapExactETHForTokens as any)(amountOutMin, path, wallet.address, deadline, { value: amountIn })
        const receipt = await tx.wait()
        if (receipt?.status == 0) {
          throw `status tx 0`
        }
        return { txHash: tx.hash, amountOut: amounts[1] }
      } else if (isTokenToETH) {
        // Token to ETH swap
        const tokenContract = new Contract(params.fromToken, abi, wallet)
        const amountIn = params.amount
        const allowance = await (tokenContract.allowance as any)(wallet.address, routerAddress)
        if (allowance < amountIn) {
          const approveTx = await (tokenContract.approve as any)(routerAddress, BlockchainUtils.maxUint256)
          await approveTx.wait()
        }
        const amounts = await (router.getAmountsOut as any)(amountIn, path)
        const amountOutMin = (amounts[1] * BigInt(100 - slippage)) / 100n
        const tx = await (router.swapExactTokensForETH as any)(amountIn, amountOutMin, path, wallet.address, deadline)
        const receipt = await tx.wait()
        if (receipt?.status == 0) {
          throw `status tx 0`
        }
        return { txHash: tx.hash, amountOut: amounts[1] }
      } else {
        // Token to Token swap
        const fromTokenContract = new Contract(params.fromToken, abi, wallet)
        const amountIn = params.amount
        const allowance = await (fromTokenContract.allowance as any)(wallet.address, routerAddress)
        if (allowance < amountIn) {
          const approveTx = await (fromTokenContract.approve as any)(routerAddress, BlockchainUtils.maxUint256)
          await approveTx.wait()
        }
        const amounts = await (router.getAmountsOut as any)(amountIn, path)
        const amountOutMin = (amounts[amounts.length - 1] * BigInt(100 - slippage)) / 100n
        const tx = await (router.swapExactTokensForTokens as any)(amountIn, amountOutMin, path, wallet.address, deadline)
        const receipt = await tx.wait()
        if (receipt?.status == 0) {
          throw `status tx 0`
        }
        return { txHash: tx.hash, amountOut: amounts[amounts.length - 1] }
      }
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:swapToken: ${e}`)
      return { error: e }
    }
  }

  async quoteSwap(params: { fromToken: string; toToken: string; amount: bigint }): Promise<{ amountOut?: bigint; error?: string; route?: any }> {
    try {
      const routerAddress = dex.uniswapV2.router
      const wethAddress = dex.uniswapV2.weth
      const router = new Contract(routerAddress, abi, this.provider._real)
      const isETHToToken = params.fromToken.toLowerCase() === `eth`
      const isTokenToETH = params.toToken.toLowerCase() === `eth`

      let path: string[]
      if (isETHToToken) {
        path = [wethAddress, params.toToken]
      } else if (isTokenToETH) {
        path = [params.fromToken, wethAddress]
      } else {
        path = [params.fromToken, wethAddress, params.toToken]
      }

      const amountIn = params.amount
      const amounts = await (router.getAmountsOut as any)(amountIn, path)
      const amountOut = amounts[amounts.length - 1]
      return {
        amountOut,
        route: path
      }
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:quoteSwap: ${e}`)
      return { error: e }
    }
  }
}
