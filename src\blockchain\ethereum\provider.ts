import { JsonRpc<PERSON><PERSON><PERSON> } from "ethers"
import { BlockchainConfig } from "../config"
import { log } from "../../utils/log"

const stateInternal = new Map()
const { rpcEndpoint } = BlockchainConfig.get(`ethereum`)

export class BlockchainEthereumProvider {
  private name = `BlockchainEthereumProvider`
  public _real: JsonRpcProvider = stateInternal.get(`provider`)
  constructor() {
    if (!this._real) {
      this._real = new JsonRpcProvider(rpcEndpoint)
      stateInternal.set(`provider`, this._real)
    }
  }

  async testConnection() {
    try {
      const blockNumber = await this._real.getBlockNumber()
      return blockNumber > 0
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:testConnection: ${e}`)
      return { error: e }
    }
  }

  async getCurrentBlock(): Promise<{ result?: number; error?: string }> {
    try {
      const result = await this._real.getBlockNumber()
      return { result }
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:getCurrentBlock: ${e}`)
      return { error: e }
    }
  }

  async getNetworkInfo(): Promise<{ chainId?: number; name?: string; error?: string }> {
    try {
      const network = await this._real.getNetwork()
      return {
        chainId: Number(network.chainId),
        name: network.name
      }
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:getNetworkInfo: ${e}`)
      return { error: e }
    }
  }

  disconnect(): void {
    try {
      this._real.destroy()
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:disconnect: ${e}`)
    }
  }
}
