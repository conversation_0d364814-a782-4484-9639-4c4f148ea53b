import { isAddress, Wallet } from "ethers"
import { BlockchainEthereumProvider } from "./provider"
import { BlockchainConfig } from "../config"
import { BlockchainUtils } from "../utils"
import { log } from "../../utils/log"

const { chainDecimals } = BlockchainConfig.get(`ethereum`)

export class BlockchainEthereumWallet {
  private name = `BlockchainEthereumWallet`
  private provider = new BlockchainEthereumProvider()
  constructor() {}

  async send(privateKey: string, toAddress: string, amount: bigint): Promise<{ result?: string; error?: string }> {
    try {
      const wallet = new Wallet(privateKey, this.provider._real)
      const balance = await this.provider._real.getBalance(wallet.address)
      const gasPrice = await this.provider._real.getFeeData()
      const gasLimit = 21000n // Standard ETH transfer gas limit
      const estimatedGasCost = gasLimit * (gasPrice.gasPrice || 0n)
      const totalCost = amount + estimatedGasCost
      if (balance < totalCost) {
        const formatUnits = BlockchainUtils.formatUnits
        return { error: `Insufficient balance. Need ${formatUnits(totalCost, chainDecimals)} ETH but have ${formatUnits(balance, chainDecimals)} ETH` }
      }

      // Create and send transaction
      const tx = await wallet.sendTransaction({
        to: toAddress,
        value: amount,
        gasLimit,
        gasPrice: gasPrice.gasPrice
      })

      // Wait for confirmation
      const receipt = await tx.wait()
      if (receipt?.status === 0) {
        throw `status tx 0`
      }
      return { result: tx.hash }
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:sendNative: ${e}`)
      return { error: e }
    }
  }

  async estimateFee(amount: string, format = false): Promise<{ result?: string | bigint; error?: string }> {
    try {
      const gasPrice = await this.provider._real.getFeeData()
      const gasLimit = 21000n // Standard ETH transfer gas limit
      const estimatedGasCost = gasLimit * (gasPrice.gasPrice || 0n)
      if (format === true) {
        return { result: BlockchainUtils.formatUnits(estimatedGasCost, chainDecimals) }
      }
      return { result: estimatedGasCost }
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:estimateFee: ${e}`)
      return { error: e }
    }
  }

  async getBalance(address: string, format = false): Promise<{ result?: bigint | string; error?: string }> {
    try {
      const balance = await this.provider._real.getBalance(address)
      if (format === true) {
        const result = BlockchainUtils.formatUnits(balance, chainDecimals)
        return { result }
      }
      return { result: balance }
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:getBalance: ${e}`)
      return { error: e }
    }
  }

  getAddress(privateKey: string) {
    return new Wallet(privateKey).address
  }

  generate() {
    const { address, privateKey } = Wallet.createRandom()
    return { address, privateKey }
  }

  isValidAddress(address: string): boolean {
    return isAddress(address)
  }
}
