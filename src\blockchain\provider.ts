import { BlockchainEthereumProvider } from "./ethereum/provider"
import { BlockchainSolanaProvider } from "./solana/provider"

const MAP = {
  ethereum: new BlockchainEthereumProvider(),
  solana: new BlockchainSolanaProvider()
}

export class BlockchainProvider {
  static get(chainName: string) {
    const target = MAP[chainName as keyof typeof MAP]
    if (!target) {
      throw new Error(`Unsupported chain: ${chainName}`)
    }

    // Proxy untuk auto-forward semua properti/method ke target
    return new Proxy(target, {
      get(_, prop, receiver) {
        return Reflect.get(target, prop, receiver)
      },
      set(_, prop, value, receiver) {
        return Reflect.set(target, prop, value, receiver)
      }
    })
  }
}
