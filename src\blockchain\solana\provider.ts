import { Connection } from "@solana/web3.js"
import { BlockchainConfig } from "../config"
import { log } from "../../utils/log"

const stateInternal = new Map()
const { rpcEndpoint, chainId, chainDisplayName } = BlockchainConfig.get(`solana`)

export class BlockchainSolanaProvider {
  private name = `BlockchainSolanaProvider`
  public _real: Connection = stateInternal.get(`provider`)
  constructor() {
    if (!this._real) {
      this._real = new Connection(rpcEndpoint, `confirmed`)
      stateInternal.set(`provider`, this._real)
    }
  }

  async testConnection(): Promise<boolean> {
    try {
      const version = await this._real.getVersion()
      return !!version
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:testConnection: ${e}`)
      return false
    }
  }

  async getCurrentBlock(): Promise<{ result?: number; error?: string }> {
    try {
      const result = await this._real.getSlot()
      return { result }
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:getCurrentBlock: ${e}`)
      return { error: e }
    }
  }

  async getNetworkInfo(): Promise<{ chainId?: number; name?: string; error?: string }> {
    try {
      return {
        chainId,
        name: chainDisplayName
      }
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:getNetworkInfo: ${e}`)
      return { error: e }
    }
  }

  disconnect(): void {
    try {
      // Solana Connection doesn't require explicit cleanup
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:disconnect: ${e}`)
    }
  }
}
