import bs58 from "bs58"
import { Keypair, PublicKey, SystemProgram, Transaction, sendAndConfirmTransaction, LAMPORTS_PER_SOL } from "@solana/web3.js"
import { BlockchainConfig } from "../config"
import { BlockchainSolanaProvider } from "./provider"
import { log } from "../../utils/log"
import { BlockchainUtils } from "../utils"

const { chainDecimals } = BlockchainConfig.get(`ethereum`)

export class BlockchainSolanaWallet {
  private name = `BlockchainSolanaWallet`
  private provider = new BlockchainSolanaProvider()
  constructor() {}

  async send(privateKey: string, toAddress: string, amount: bigint): Promise<{ result?: string; error?: string }> {
    try {
      const secretKey = bs58.decode(privateKey) // Decode private key and create keypair
      const fromKeypair = Keypair.fromSecretKey(secretKey)
      let toPublicKey: PublicKey
      try {
        toPublicKey = new PublicKey(toAddress) // Validate recipient address
      } catch {
        return { error: `Invalid Solana address format` }
      }

      // Convert amount to lamports
      if (amount <= 0) {
        return { error: `Amount must be greater than 0` }
      }

      // Get current balance
      const balance = BigInt(await this.provider._real.getBalance(fromKeypair.publicKey))

      // Estimate transaction fee (typical SOL transfer fee is around 5000 lamports)
      const estimatedFee = 5000n * BigInt(LAMPORTS_PER_SOL)
      const totalCost = amount + estimatedFee
      if (balance < totalCost) {
        const balanceSol = BlockchainUtils.formatUnits(balance / BigInt(LAMPORTS_PER_SOL), chainDecimals)
        const requiredSol = BlockchainUtils.formatUnits(totalCost / BigInt(LAMPORTS_PER_SOL), chainDecimals)
        return { error: `Insufficient balance. Need ${requiredSol} SOL but have ${balanceSol} SOL` }
      }

      // Create transaction
      const transaction = new Transaction().add(
        SystemProgram.transfer({
          fromPubkey: fromKeypair.publicKey,
          toPubkey: toPublicKey,
          lamports: amount
        })
      )

      // Send and confirm transaction
      const signature = await sendAndConfirmTransaction(this.provider._real, transaction, [fromKeypair], {
        commitment: `confirmed`,
        preflightCommitment: `confirmed`
      })

      return { result: signature }
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${this.name}:send: ${e}`)
      return { error: e }
    }
  }

  async estimateFee(amount: string, format = false): Promise<{ result?: string | bigint; error?: string }> {
    // try {
    // SOL transaction fees are relatively fixed
    return { result: `0.000005` } // 5000 lamports = 0.000005 SOL
    // } catch (error) {
    //   const error = JSON.stringify(error)
    //   log.error(`${BlockchainSolanaWallet.name}:estimateFee: ${error}`)
    //   return { error }
    // }
  }

  async getBalance(address: string, format = false): Promise<{ result?: bigint | string; error?: string }> {
    try {
      const publicKey = new PublicKey(address)
      const balance = await this.provider._real.getBalance(publicKey)
      if (format === true) {
        return { result: (balance / LAMPORTS_PER_SOL).toString() }
      }
      return { result: BigInt(balance) }
    } catch (error) {
      const e = JSON.stringify(error)
      log.error(`${BlockchainSolanaWallet.name}:getBalance: ${e}`)
      return { error: e }
    }
  }

  public generate() {
    const { publicKey, secretKey } = Keypair.generate()
    const address = publicKey.toBase58()
    const privateKey = bs58.encode(secretKey)
    return { address, privateKey }
  }

  public getAddress(privateKey: string) {
    const decode = bs58.decode(privateKey)
    const { publicKey } = Keypair.fromSecretKey(decode)
    return publicKey.toBase58()
  }

  public isValidAddress(address: string): boolean {
    try {
      new PublicKey(address)
      return true
    } catch {
      return false
    }
  }
}
