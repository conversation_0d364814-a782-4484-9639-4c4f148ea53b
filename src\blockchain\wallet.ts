import { BlockchainEthereumWallet } from "./ethereum/wallet"
import { BlockchainSolanaWallet } from "./solana/wallet"

const MAP = {
  ethereum: new BlockchainEthereumWallet(),
  solana: new BlockchainSolanaWallet()
}

export class BlockchainWallet {
  static get(chainName: string) {
    const target = MAP[chainName as keyof typeof MAP]
    if (!target) {
      throw new Error(`Unsupported chain: ${chainName}`)
    }

    // Proxy untuk auto-forward semua properti/method ke target
    return new Proxy(target, {
      get(_, prop, receiver) {
        return Reflect.get(target, prop, receiver)
      },
      set(_, prop, value, receiver) {
        return Reflect.set(target, prop, value, receiver)
      }
    })
  }
}
