import { Bo<PERSON>, type Context } from "grammy"
import { log } from "./utils/log"
import { <PERSON><PERSON> } from "./handler"
import { StartPage } from "./pages/start"
import { SettingPage } from "./pages/setting"
import { LanguagePage } from "./pages/language"
import { WalletPage } from "./pages/wallet"
import { WalletDetailExportPage } from "./pages/wallet-detail-export"
import { WalletDetailDeletePage } from "./pages/wallet-detail-delete"
import { WalletCreateNamePage } from "./pages/wallet-create-name"
import { WalletImportNamePage } from "./pages/wallet-import-name"
import { HelpPage } from "./pages/help"
import { GeneralErrorPage } from "./pages/general-error"
import { WalletImportPrivateKeyPage } from "./pages/wallet-import-private-key"
import { GeneralErrorInputNotFoundPage } from "./pages/general-error-input-notfound"
import { WalletDetailDeleteExecutePage } from "./pages/wallet-detail-delete-execute"
import { WalletDetailExportExecutePage } from "./pages/wallet-detail-export-execute"
import { WalletDetailPage } from "./pages/wallet-detail"
import { WalletDetailWithdrawAddressPage } from "./pages/wallet-detail-withdraw-address"
import { WalletDetailWithdrawAmountPage } from "./pages/wallet-detail-withdraw-amount"
import { WalletDetailWithdrawExecutePage } from "./pages/wallet-detail-withdraw-execute"
import { GeneralErrorSessionPage } from "./pages/general-error-session"
import { ReferralPage } from "./pages/referral"
import { WalletCreatePage } from "./pages/wallet-create"
import { WalletImportPage } from "./pages/wallet-import"
import { UserGuard } from "./guards/user"
import { Name } from "./name"
import { ConfigPage } from "./pages/config"
import { ConfigCreateAddressPage } from "./pages/config-create-address"
import { ConfigCreateAmountPage } from "./pages/config-create-amount"
import { ConfigCreateRebuyPage } from "./pages/config-create-rebuy"
import { ConfigCreateSellPage } from "./pages/config-create-sell"
import { ConfigCreateMaxPendingSellPage } from "./pages/config-create-max-pending-sell"
import { ConfigCreateExecutePage } from "./pages/config-create-execute"
import { ConfigCreatePage } from "./pages/config-create"
import { ConfigCreateNamePage } from "./pages/config-create-name"
import { ConfigDetailPage } from "./pages/config-detail"
import { ConfigDetailDeletePage } from "./pages/config-detail-delete"
import { ConfigDetailDeleteExecutePage } from "./pages/config-detail-delete-execute"

/**
 * Production-optimized Telegram bot class
 * Consolidated bot implementation with distributed callback handling for maximum maintainability
 */
export class RunBot {
  private bot: Bot
  constructor() {
    this.bot = new Bot(process.env.TELEGRAM_TOKEN as string)
    this.bot.catch((err) => log.error(`Telegram bot error: ${JSON.stringify(err)}`)) // Error handling
    new Promise((resolve) => {
      this.bot.start({
        onStart: async (botInfo) => {
          log.success(`Telegram bot started: ${botInfo.username}`)
          // const admin = await ModelAdmin.getFirstByRole(`super`)
          // if (!admin) {
          //   log.error(`Super admin role not yet`)
          //   return reject()
          // }
          resolve(true)
        }
      })
    })
      .then(() => {
        this.bot.on(`callback_query:data`, this.onCallbackQueryData.bind(this)) // Handle callback queries from inline keyboards
        this.bot.on(`message:text`, this.onMessageText.bind(this)) // Handle text messages
      })
      .catch(() => {
        process.exit()
      })
  }

  async onCallbackQueryData(ctx: Context): Promise<void> {
    const handler = new Handler(ctx)
    try {
      const { answerCbQuery, callbackData: ca } = handler
      await answerCbQuery()
      const user = await new UserGuard(handler).ensureValid()
      if (user === null) {
        return
      }

      if (ca === `noop`) (() => {})()
      else if (ca === Name.start) new StartPage(handler).show()
      else if (ca === Name.setting) new SettingPage(handler).show()
      else if (ca === Name.language) new LanguagePage(handler).show()
      else if (ca === Name.wallet) new WalletPage(handler).show()
      else if (ca === Name.walletCreate) new WalletCreatePage(handler).show()
      else if (ca === Name.walletImport) new WalletImportPage(handler).show()
      else if (ca === Name.help) new HelpPage(handler).show()
      else if (ca === Name.referral) new ReferralPage(handler).show()
      else if (ca === Name.config) new ConfigPage(handler).show()
      else if (ca === Name.configCreate) new ConfigCreatePage(handler).show()
      else if (ca === Name.configCreateExecute) new ConfigCreateExecutePage(handler).show()
      else if (ca.startsWith(`${Name.configDetail}:`)) new ConfigDetailPage(handler).show()
      else if (ca.startsWith(`${Name.configCreate}:`)) new ConfigCreatePage(handler).show()
      else if (ca.startsWith(`${Name.configCreateName}:`)) new ConfigCreateNamePage(handler).show()
      else if (ca.startsWith(`${Name.configDetailDelete}:`)) new ConfigDetailDeletePage(handler).show()
      else if (ca.startsWith(`${Name.configDetailDeleteExecute}:`)) new ConfigDetailDeleteExecutePage(handler).show()
      else if (ca.startsWith(`${Name.setting}:`)) new SettingPage(handler).show()
      else if (ca.startsWith(`${Name.wallet}:`)) new WalletPage(handler).show()
      else if (ca.startsWith(`${Name.walletCreateName}:`)) new WalletCreateNamePage(handler).show()
      else if (ca.startsWith(`${Name.walletImportName}:`)) new WalletImportNamePage(handler).show()
      else if (ca.startsWith(`${Name.walletDetail}:`)) new WalletDetailPage(handler).show()
      else if (ca.startsWith(`${Name.walletDetailExport}:`)) new WalletDetailExportPage(handler).show()
      else if (ca.startsWith(`${Name.walletDetailExportExecute}:`)) new WalletDetailExportExecutePage(handler).show()
      else if (ca.startsWith(`${Name.walletDetailDelete}:`)) new WalletDetailDeletePage(handler).show()
      else if (ca.startsWith(`${Name.walletDetailDeleteExecute}:`)) new WalletDetailDeleteExecutePage(handler).show()
      else if (ca.startsWith(`${Name.walletDetailWithdrawAddress}:`)) new WalletDetailWithdrawAddressPage(handler).show()
      else if (ca.startsWith(`${Name.walletDetailWithdrawExecute}:`)) new WalletDetailWithdrawExecutePage(handler).show()
    } catch (error) {
      new GeneralErrorPage(handler).show(`onCallbackQueryData`, error)
    }
  }

  async onMessageText(ctx: Context): Promise<void> {
    const handler = new Handler(ctx)
    try {
      const input = handler.msgText.trim()
      const user = await new UserGuard(handler).ensureValid()
      if (user === null) {
        return
      }

      // Available command
      if (input === `/start`) {
        return new StartPage(handler).show()
      }

      // Check if user has an active session
      const session = await handler.sessionGet()
      if (!session) {
        return new GeneralErrorInputNotFoundPage(handler).show()
      }

      // Handle session input
      const { method, params } = session
      if (method === `it-${Name.walletCreateName}`) new WalletCreateNamePage(handler).input(params, input)
      else if (method === `it-${Name.walletImportName}`) new WalletImportNamePage(handler).input(params, input)
      else if (method === `it-${Name.walletImportPrivateKey}`) new WalletImportPrivateKeyPage(handler).input(params, input)
      else if (method === `it-${Name.walletDetailWithdrawAddress}`) new WalletDetailWithdrawAddressPage(handler).input(params, input)
      else if (method === `it-${Name.walletDetailWithdrawAmount}`) new WalletDetailWithdrawAmountPage(handler).input(params, input)
      else if (method === `it-${Name.configCreateName}`) new ConfigCreateNamePage(handler).input(params, input)
      else if (method === `it-${Name.configCreateAddress}`) new ConfigCreateAddressPage(handler).input(params, input)
      else if (method === `it-${Name.configCreateAmount}`) new ConfigCreateAmountPage(handler).input(params, input)
      else if (method === `it-${Name.configCreateRebuy}`) new ConfigCreateRebuyPage(handler).input(params, input)
      else if (method === `it-${Name.configCreateSell}`) new ConfigCreateSellPage(handler).input(params, input)
      else if (method === `it-${Name.configCreateMaxPendingSell}`) new ConfigCreateMaxPendingSellPage(handler).input(params, input)
      else new GeneralErrorSessionPage(handler).show()
    } catch (error) {
      new GeneralErrorPage(handler).show(`onMessageText`, error)
    }
  }
}
