/**
 * Database connection configuration
 *
 * This file sets up the database connection and exports the database instance
 * along with schema tables for use throughout the application.
 */

import { join } from "node:path"
import postgres from "postgres"
import { PGlite } from "@electric-sql/pglite"
import { drizzle } from "drizzle-orm/postgres-js"
import { drizzle as drizzlePgLite } from "drizzle-orm/pglite"
import { userSchema } from "./schema/user"
import { walletSchema } from "./schema/wallet"
import { feeSchema } from "./schema/fee"
import { userBannedSchema } from "./schema/user-banned"
import { adminSchema } from "./schema/admin"

const isUrlLocalPath = process.env.DATABASE_URL?.startsWith(`./`)
export const db = isUrlLocalPath
  ? drizzlePgLite(new PGlite(join(process.cwd(), process.env.DATABASE_URL as string)), {
      schema: {
        user: userSchema,
        wallet: walletSchema,
        fee: feeSchema,
        userBanned: userBannedSchema,
        admin: adminSchema
      },
      casing: `snake_case`
    })
  : drizzle(postgres(process.env.DATABASE_URL as string, { prepare: false }), {
      schema: {
        user: userSchema,
        wallet: walletSchema,
        fee: feeSchema,
        userBanned: userBannedSchema,
        admin: adminSchema
      },
      casing: `snake_case`
    })
