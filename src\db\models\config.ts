import { and, desc, eq } from "drizzle-orm"
import { db } from ".."
import { log } from "../../utils/log"
import { configSchema, type TypeConfigInsert, type TypeConfigRecord } from "../schema/config"
import { createId } from "@paralleldrive/cuid2"
import { walletSchema } from "../schema/wallet"
import { userSchema } from "../schema/user"

export class ConfigModel {
  private static name = `ConfigModel`
  private static readonly ALLOWED_STATUS = new Set([0, 1, 2, 3]) // 0: inactive, 1: active, 2: success, 3: failed
  private static logError(error: any, func: string, ret: any = null) {
    log.error(`${this.name}:${func}: ${JSON.stringify(error)}`)
    return ret
  }

  private static validatePercent(val: number | undefined) {
    if (val === undefined) return true
    return Number.isFinite(val) && val >= 0 && val <= 100
  }

  /** Get a config by ID. */
  static async getById(id: string): Promise<TypeConfigRecord | null> {
    try {
      const [row] = await db.select().from(configSchema).where(eq(configSchema.id, id)).limit(1)
      return row || null
    } catch (error) {
      return this.logError(error, `getById`)
    }
  }

  /** Get a config by ID ensuring the referenced wallet exists (single JOIN). */
  static async getByIdWithWallet(id: string): Promise<TypeConfigRecord | null> {
    try {
      const [row] = await db
        .select({
          id: configSchema.id,
          walletId: configSchema.walletId,
          name: configSchema.name,
          amount: configSchema.initAmountIn,
          path: configSchema.path,
          percentRebuy: configSchema.rebuyBps,
          percentSell: configSchema.sellBps,
          maxPendingSell: configSchema.maxPendingSell,
          status: configSchema.status,
          createdAt: configSchema.createdAt,
          updatedAt: configSchema.updatedAt
        })
        .from(configSchema)
        .innerJoin(walletSchema, eq(configSchema.walletId, walletSchema.id))
        .where(eq(configSchema.id, id))
        .limit(1)
      return row || null
    } catch (error) {
      return this.logError(error, `getByIdWithWallet`)
    }
  }

  /** Get a config by name for a specific user (via wallet.userId). */
  static async getByNameForUser(name: string, userId: number): Promise<TypeConfigRecord | null> {
    try {
      const [row] = await db
        .select({
          id: configSchema.id,
          walletId: configSchema.walletId,
          name: configSchema.name,
          amount: configSchema.initAmountIn,
          path: configSchema.path,
          percentRebuy: configSchema.rebuyBps,
          percentSell: configSchema.sellBps,
          maxPendingSell: configSchema.maxPendingSell,
          status: configSchema.status,
          createdAt: configSchema.createdAt,
          updatedAt: configSchema.updatedAt
        })
        .from(configSchema)
        .innerJoin(walletSchema, eq(configSchema.walletId, walletSchema.id))
        .innerJoin(userSchema, eq(walletSchema.userId, userSchema.id))
        .where(and(eq(configSchema.name, name), eq(walletSchema.userId, userId)))
        .limit(1)
      return row || null
    } catch (error) {
      return this.logError(error, `getByNameForUser`)
    }
  }

  /** Get recent configs for a wallet (newest first) */
  static async getByWalletId(walletId: string, limit: number = 50): Promise<Array<TypeConfigRecord>> {
    try {
      const rows = await db.select().from(configSchema).where(eq(configSchema.walletId, walletId)).orderBy(desc(configSchema.createdAt)).limit(limit)
      return rows
    } catch (error) {
      return this.logError(error, `getByWalletId`, [])
    }
  }

  /** Get recent configs for a user (newest first) */
  static async getAllForUser(userId: number, limit: number = 50): Promise<Array<TypeConfigRecord>> {
    try {
      const rows = await db
        .select({
          id: configSchema.id,
          walletId: configSchema.walletId,
          name: configSchema.name,
          amount: configSchema.initAmountIn,
          path: configSchema.path,
          percentRebuy: configSchema.rebuyBps,
          percentSell: configSchema.sellBps,
          maxPendingSell: configSchema.maxPendingSell,
          status: configSchema.status,
          createdAt: configSchema.createdAt,
          updatedAt: configSchema.updatedAt
        })
        .from(configSchema)
        .innerJoin(walletSchema, eq(configSchema.walletId, walletSchema.id))
        .where(eq(walletSchema.userId, userId)) // userId (wallet owner)
        .orderBy(desc(configSchema.createdAt))
        .limit(limit)

      return rows
    } catch (error) {
      return this.logError(error, `getAllForUser`, [])
    }
  }

  /** Create new trade config */
  static async create(data: Omit<TypeConfigInsert, `id`>): Promise<TypeConfigRecord | null> {
    try {
      // Basic validation
      if (!data?.walletId) return null
      if (typeof data.amount !== `bigint` || data.amount < 0n) return null
      if (!this.validatePercent(data.percentRebuy as any)) return null
      if (typeof data.maxPendingSell !== `number` || data.maxPendingSell < 0) return null

      const status = (data as any).status ?? 0
      if (!this.ALLOWED_STATUS.has(status)) return null

      const id = createId()
      const [row] = await db
        .insert(configSchema)
        .values({
          id,
          walletId: data.walletId,
          name: data.name,
          amount: data.amount,
          path: data.path ?? [],
          percentRebuy: data.percentRebuy,
          percentSell: data.percentSell,
          maxPendingSell: data.maxPendingSell,
          status
        })
        .returning()

      return row || (await this.getById(id))
    } catch (error) {
      return this.logError(error, `create`)
    }
  }

  /** Generic update for allowed fields */
  static async update(id: string, patch: Partial<Pick<TypeConfigInsert, `amount` | `path` | `percentRebuy` | `percentSell` | `maxPendingSell` | `status`>>): Promise<TypeConfigRecord | null> {
    try {
      const updates: any = {}
      if (patch.amount !== undefined) {
        if (typeof patch.amount !== `bigint` || patch.amount < 0n) return null
        updates.amount = patch.amount
      }
      if (patch.path !== undefined) {
        updates.path = patch.path
      }
      if (patch.percentRebuy !== undefined) {
        if (!this.validatePercent(patch.percentRebuy as any)) return null
        updates.percentRebuy = patch.percentRebuy
      }
      if (patch.percentSell !== undefined) {
        if (!this.validatePercent(patch.percentSell as any)) return null
        updates.percentSell = patch.percentSell
      }
      if (patch.maxPendingSell !== undefined) {
        if (typeof patch.maxPendingSell !== `number` || patch.maxPendingSell < 0) return null
        updates.maxPendingSell = patch.maxPendingSell
      }
      if (patch.status !== undefined) {
        if (!this.ALLOWED_STATUS.has(patch.status as any)) return null
        updates.status = patch.status
      }

      if (Object.keys(updates).length === 0) {
        return await this.getById(id)
      }

      const [row] = await db.update(configSchema).set(updates).where(eq(configSchema.id, id)).returning()
      return row || (await this.getById(id))
    } catch (error) {
      return this.logError(error, `update`)
    }
  }

  /** Update only status */
  static async updateStatus(id: string, status: 0 | 1 | 2 | 3): Promise<TypeConfigRecord | null> {
    try {
      if (!this.ALLOWED_STATUS.has(status)) return null
      const [row] = await db.update(configSchema).set({ status }).where(eq(configSchema.id, id)).returning()
      return row || (await this.getById(id))
    } catch (error) {
      return this.logError(error, `updateStatus`)
    }
  }

  /** Delete by id (no ownership check) */
  static async delete(id: string) {
    try {
      await db.delete(configSchema).where(eq(configSchema.id, id))
      return true
    } catch (error) {
      return this.logError(error, `delete`, false)
    }
  }

  /** Secure remove by id and wallet owner (matches page usage: remove(id, owner)) */
  static async remove(id: string, walletId: string) {
    try {
      const [row] = await db.select().from(configSchema).where(eq(configSchema.id, id)).limit(1)
      if (!row || row.walletId !== walletId) {
        return false
      }
      await db.delete(configSchema).where(eq(configSchema.id, id))
      return true
    } catch (error) {
      return this.logError(error, `remove`, false)
    }
  }
}
