import { and, desc, eq } from "drizzle-orm"
import { db } from ".."
import { log } from "../../utils/log"
import { txSchema, type TypeTxRecord, type TypeTxInsert } from "../schema/tx"
import { walletSchema } from "../schema/wallet"
import { configSchema } from "../schema/config"

export class TxModel {
  private static name = `TxModel`
  private static logError(error: any, func: string, ret: any = null) {
    log.error(`${this.name}:${func}: ${JSON.stringify(error)}`)
    return ret
  }

  /** Get a transaction by its hash */
  static async getByHash(hash: string) {
    try {
      const [row] = await db //
        .select()
        .from(txSchema)
        .where(eq(txSchema.hash, hash))
        .limit(1)
      return row || null
    } catch (error) {
      return this.logError(error, `getByHash`)
    }
  }

  /** Get recent transactions for a config (newest first) */
  static async getByConfigId(configId: string, limit: number = 50) {
    try {
      return await db //
        .select()
        .from(txSchema)
        .where(eq(txSchema.configId, configId))
        .orderBy(desc(txSchema.createdAt))
        .limit(limit)
    } catch (error) {
      return this.logError(error, `getByConfigId`, [])
    }
  }

  /** Get recent transactions for a chain (optionally by type) */
  static async getRecentByChain(chain: string, type?: TypeTxRecord[`type`], limit: number = 50) {
    try {
      const where = type ? and(eq(txSchema.chain, chain), eq(txSchema.type, type)) : eq(txSchema.chain, chain)
      return await db
        .select()
        .from(txSchema)
        .where(where as any)
        .orderBy(desc(txSchema.createdAt))
        .limit(limit)
    } catch (error) {
      return this.logError(error, `getRecentByChain`, [])
    }
  }

  /**
   * Create a new transaction record
   * - Validates required fields and basic constraints
   * - Prevents duplicate creation if hash already exists
   */
  static async create(data: TypeTxInsert) {
    try {
      // Basic required validations
      if (!data?.hash || typeof data.hash !== `string`) return null
      if (!data.from || !data.to) return null
      if (!data.chain || !data.configId) return null
      if (typeof data.amountIn !== `bigint` || data.amountIn < 0n) return null

      // Prevent duplicates
      const existing = await this.getByHash(data.hash)
      if (existing) return existing

      const [row] = await db
        .insert(txSchema)
        .values({
          configId: data.configId,
          chain: data.chain,
          hash: data.hash,
          from: data.from,
          to: data.to,
          type: data.type,
          amountIn: data.amountIn,
          amountOut: data.amountOut,
          slippage: data.slippage,
          tokenIn: data.tokenIn,
          tokenOut: data.tokenOut,
          status: data.status
        })
        .returning()

      return row || null
    } catch (error) {
      return this.logError(error, `create`)
    }
  }

  /** Update transaction status (1: success, 2: failed) */
  static async updateStatus(hash: string, status: TypeTxRecord[`status`]) {
    try {
      const [row] = await db //
        .update(txSchema)
        .set({ status })
        .where(eq(txSchema.hash, hash))
        .returning()
      return row || null
    } catch (error) {
      return this.logError(error, `updateStatus`)
    }
  }

  /** Get a transaction with wallet owner (user id) for quick attribution */
  static async getByHashWithOwner(hash: string) {
    try {
      const [row] = await db
        .select({
          configId: txSchema.configId,
          chain: txSchema.chain,
          hash: txSchema.hash,
          from: txSchema.from,
          to: txSchema.to,
          type: txSchema.type,
          amountIn: txSchema.amountIn,
          amountOut: txSchema.amountOut,
          tokenIn: txSchema.tokenIn,
          tokenOut: txSchema.tokenOut,
          status: txSchema.status,
          createdAt: txSchema.createdAt,
          userId: walletSchema.userId
        })
        .from(txSchema)
        .innerJoin(configSchema, eq(txSchema.configId, configSchema.id))
        .innerJoin(walletSchema, eq(configSchema.walletId, walletSchema.id))
        .where(eq(txSchema.hash, hash))
        .limit(1)

      return row || null
    } catch (error) {
      return this.logError(error, `getByHashWithOwner`)
    }
  }

  /** Delete a transaction by hash */
  static async delete(hash: string) {
    try {
      await db //
        .delete(txSchema)
        .where(eq(txSchema.hash, hash))
      return true
    } catch (error) {
      return this.logError(error, `delete`, false)
    }
  }
}
