import { eq, and, desc, lt, sql } from "drizzle-orm"
import { db } from ".."
import { log } from "../../utils/log"
import { userBannedSchema, type TypeUserBannedRecord, type TypeUserBannedInsert } from "../schema/user-banned"
import { userSchema } from "../schema/user"

export class UserBannedModel {
  private static name = `UserBannedModel`
  private static logError(error: any, func: string, ret: any = null) {
    log.error(`${this.name}:${func}: ${JSON.stringify(error)}`)
    return ret
  }

  /**
   * Generate a unique ban ID
   * @returns A unique ban identifier (format: BAN-YYYY-XXX)
   */
  private static generateBanId(): string {
    const year = new Date().getFullYear()
    const timestamp = Date.now().toString().slice(-6) // Last 6 digits of timestamp
    return `BAN-${year}-${timestamp}`
  }

  /**
   * Check if a user is currently banned
   * @param userId Telegram user ID
   * @returns True if user is banned, false otherwise
   */
  static async isUserBanned(userId: number): Promise<boolean> {
    try {
      const activeBan = await db
        .select({ id: userBannedSchema.id })
        .from(userBannedSchema)
        .where(
          and(
            eq(userBannedSchema.userId, userId),
            eq(userBannedSchema.isActive, true),
            // Check if permanent or not expired
            sql`(${userBannedSchema.isPermanent} = true OR ${userBannedSchema.expiresAt} > NOW())`
          )
        )
        .limit(1)

      return activeBan.length > 0
    } catch (error) {
      return this.logError(error, `isUserBanned`, false)
    }
  }

  /**
   * Get active ban details for a user
   * @param userId Telegram user ID
   * @returns Active ban record or null if not banned
   */
  static async getActiveBan(userId: number): Promise<TypeUserBannedRecord | null> {
    try {
      const [row] = await db
        .select()
        .from(userBannedSchema)
        .where(and(eq(userBannedSchema.userId, userId), eq(userBannedSchema.isActive, true), sql`(${userBannedSchema.isPermanent} = true OR ${userBannedSchema.expiresAt} > NOW())`))
        .orderBy(desc(userBannedSchema.createdAt))
        .limit(1)

      return row || null
    } catch (error) {
      return this.logError(error, `getActiveBan`)
    }
  }

  /**
   * Create a new ban for a user
   * @param userId User to ban
   * @param reason Reason for the ban
   * @param bannedBy Admin user who issued the ban
   * @param description Optional detailed description
   * @param isPermanent Whether this is a permanent ban
   * @param expiresAt When the ban expires (required for temporary bans)
   * @returns The created ban record or null on error
   */
  static async banUser(userId: number, reason: string, bannedBy: number, description?: string, isPermanent: boolean = false, expiresAt?: Date): Promise<TypeUserBannedRecord | null> {
    try {
      // Validate temporary ban has expiry date
      if (!isPermanent && !expiresAt) {
        throw new Error(`Temporary bans must have an expiry date`)
      }

      // Check if user is already banned
      const existingBan = await this.getActiveBan(userId)
      if (existingBan) {
        return existingBan
      }

      const banData: TypeUserBannedInsert = {
        userId,
        banId: this.generateBanId(),
        reason,
        description,
        bannedBy,
        isActive: true,
        isPermanent,
        expiresAt: isPermanent ? null : expiresAt
      }

      const [row] = await db.insert(userBannedSchema).values(banData).returning()
      return row || null
    } catch (error) {
      return this.logError(error, `banUser`)
    }
  }

  /**
   * Unban a user by deactivating their active ban
   * @param userId User to unban
   * @param unbannedBy Admin user who lifted the ban
   * @param unbanReason Reason for lifting the ban
   * @returns True if successfully unbanned, false otherwise
   */
  static async unbanUser(userId: number, unbannedBy: number, unbanReason: string): Promise<boolean> {
    try {
      const activeBan = await this.getActiveBan(userId)
      if (!activeBan) {
        throw `No active ban found for user ${userId}`
      }

      await db
        .update(userBannedSchema)
        .set({
          isActive: false,
          unbanReason,
          unbannedBy,
          unbannedAt: new Date(),
          updatedAt: new Date()
        })
        .where(eq(userBannedSchema.id, activeBan.id))

      return true
    } catch (error) {
      return this.logError(error, `unbanUser`, false)
    }
  }

  /**
   * Get ban history for a user
   * @param userId User ID to get history for
   * @param limit Maximum number of records to return
   * @returns Array of ban records
   */
  static async getBanHistory(userId: number, limit: number = 10): Promise<TypeUserBannedRecord[]> {
    try {
      const bans = await db.select().from(userBannedSchema).where(eq(userBannedSchema.userId, userId)).orderBy(desc(userBannedSchema.createdAt)).limit(limit)
      return bans
    } catch (error) {
      return this.logError(error, `getBanHistory`, [])
    }
  }

  /**
   * Get all active bans (for admin purposes)
   * @param limit Maximum number of records to return
   * @returns Array of active ban records with user information
   */
  static async getActiveBans(limit: number = 50) {
    try {
      const bans = await db
        .select({
          ban: userBannedSchema,
          user: {
            id: userSchema.id,
            username: userSchema.username,
            fullname: userSchema.fullname
          }
        })
        .from(userBannedSchema)
        .leftJoin(userSchema, eq(userBannedSchema.userId, userSchema.id))
        .where(and(eq(userBannedSchema.isActive, true), sql`(${userBannedSchema.isPermanent} = true OR ${userBannedSchema.expiresAt} > NOW())`))
        .orderBy(desc(userBannedSchema.createdAt))
        .limit(limit)

      return bans
    } catch (error) {
      return this.logError(error, `getActiveBans`, [])
    }
  }

  /**
   * Get ban by ban ID
   * @param banId Unique ban identifier
   * @returns Ban record or null if not found
   */
  static async getBanById(banId: string): Promise<TypeUserBannedRecord | null> {
    try {
      const [row] = await db.select().from(userBannedSchema).where(eq(userBannedSchema.banId, banId)).limit(1)
      return row || null
    } catch (error) {
      return this.logError(error, `getBanById`, [])
    }
  }

  /**
   * Clean up expired bans by marking them as inactive
   * This should be run periodically to maintain database hygiene
   * @returns Number of bans that were deactivated
   */
  static async cleanupExpiredBans(): Promise<number> {
    try {
      const result = await db
        .update(userBannedSchema)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(and(eq(userBannedSchema.isActive, true), eq(userBannedSchema.isPermanent, false), lt(userBannedSchema.expiresAt, new Date())))
        .returning()

      const count = result.length
      return count
    } catch (error) {
      return this.logError(error, `cleanupExpiredBans`, 0)
    }
  }

  /**
   * Get ban statistics
   * @returns Object containing various ban statistics
   */
  static async getBanStats() {
    try {
      const [totalBans] = await db.select({ count: sql<number>`count(*)` }).from(userBannedSchema)

      const [activeBans] = await db
        .select({ count: sql<number>`count(*)` })
        .from(userBannedSchema)
        .where(and(eq(userBannedSchema.isActive, true), sql`(${userBannedSchema.isPermanent} = true OR ${userBannedSchema.expiresAt} > NOW())`))

      const [permanentBans] = await db
        .select({ count: sql<number>`count(*)` })
        .from(userBannedSchema)
        .where(and(eq(userBannedSchema.isActive, true), eq(userBannedSchema.isPermanent, true)))

      return {
        totalBans: totalBans?.count || 0,
        activeBans: activeBans?.count || 0,
        permanentBans: permanentBans?.count || 0,
        temporaryBans: (activeBans?.count || 0) - (permanentBans?.count || 0)
      }
    } catch (error) {
      return this.logError(error, `getBanStats`, {
        totalBans: 0,
        activeBans: 0,
        permanentBans: 0,
        temporaryBans: 0
      })
    }
  }
}
