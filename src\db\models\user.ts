import { eq } from "drizzle-orm"
import { db } from ".."
import { log } from "../../utils/log"
import { userSchema } from "../schema/user"
import { BlockchainUtils } from "../../blockchain/utils"

export class UserModel {
  private static name = `UserModel`
  private static logError(error: any, func: string, ret: any = null) {
    log.error(`${this.name}:${func}: ${JSON.stringify(error)}`)
    return ret
  }

  /**
   * Get a user by Telegram username
   * @param username Telegram username
   * @returns The user or null if not found
   */
  static async getByUsername(username: string) {
    try {
      const [row] = await db.select().from(userSchema).where(eq(userSchema.username, username)).limit(1)
      return row || null
    } catch (error) {
      return this.logError(error, `getByUsername`)
    }
  }

  /**
   * Get a user by ID
   * @param id User ID
   * @returns The user or null if not found
   */
  static async getById(id: number) {
    try {
      const [row] = await db.select().from(userSchema).where(eq(userSchema.id, id)).limit(1)
      return row || null
    } catch (error) {
      return this.logError(error, `getById`)
    }
  }

  /**
   * Create a new user
   * @param userId Telegram user ID
   * @param username Telegram username (without @ symbol)
   * @param fullname User's full name
   * @returns The created user or null if creation failed
   */
  static async create(userId: number, username: string, fullname: string) {
    try {
      // Check if user already exists
      const existingUser = await this.getById(userId)
      if (existingUser) {
        return existingUser
      }

      // Create new user with default language
      const [row] = await db
        .insert(userSchema)
        .values({
          id: userId,
          username: username || ``,
          fullname: fullname || ``,
          isActive: true,
          fee: `0`,
          language: `en`
        })
        .returning()
      return row || (await this.getById(userId))
    } catch (error) {
      return this.logError(error, `create`)
    }
  }

  /**
   * Update user's active status
   * @param userId User ID
   * @param isActive Whether the user should be active
   * @returns Updated user or null if operation failed
   */
  static async updateActiveStatus(userId: number, isActive: boolean) {
    try {
      const [row] = await db.update(userSchema).set({ isActive }).where(eq(userSchema.id, userId)).returning()
      return row || (await this.getById(userId))
    } catch (error) {
      return this.logError(error, `updateActiveStatus`)
    }
  }

  /**
   * Update user's fee percentage
   * @param userId User ID
   * @param fee Fee percentage as string (e.g., `0.3` for 0.3%)
   * @returns Updated user or null if operation failed
   */
  static async updateFee(userId: number, fee: string) {
    try {
      // Validate fee range
      const feeNum = parseFloat(fee)
      if (isNaN(feeNum) || feeNum < 0 || feeNum > 100) {
        return null // Invalid fee percentage
      }
      const validatedFee = Math.max(0, Math.min(100, feeNum)).toString()

      const [row] = await db.update(userSchema).set({ fee: validatedFee }).where(eq(userSchema.id, userId)).returning()
      return row || (await this.getById(userId))
    } catch (error) {
      return this.logError(error, `updateFee`)
    }
  }

  /**
   * Calculate user-specific fee amount using your specified calculation mechanism
   * Uses your specified approach to avoid overflow/underflow issues
   * @param userId User ID
   * @param tradeAmount Trade amount in the smallest unit
   * @returns Object with amountAfterFee and feeAmount
   */
  static async calculateUserFee(userId: number, tradeAmount: bigint) {
    try {
      const user = await this.getById(userId)

      if (!user || user.fee === `0` || parseFloat(user.fee) === 0) {
        return { amountAfterFee: tradeAmount, feeAmount: BigInt(0) }
      }

      // Use the improved fee calculation to avoid overflow/underflow
      return BlockchainUtils.calculateFeeDeduction(tradeAmount, user.fee)
    } catch (error) {
      return this.logError(error, `calculateUserFee`, { amountAfterFee: tradeAmount, feeAmount: BigInt(0) })
    }
  }

  /**
   * Update user's language preference
   * @param userId User ID
   * @param language Language code (e.g., `en`, `es`, `fr`)
   * @returns Updated user or null if operation failed
   */
  static async updateLanguage(userId: number, language: string) {
    try {
      const [row] = await db.update(userSchema).set({ language }).where(eq(userSchema.id, userId)).returning()
      return row || (await this.getById(userId))
    } catch (error) {
      return this.logError(error, `updateLanguage`)
    }
  }

  /**
   * Get all active users
   * @returns Array of active users
   */
  static async getAllActive() {
    try {
      return await db.select().from(userSchema).where(eq(userSchema.isActive, true))
    } catch (error) {
      return this.logError(error, `getAllActive`, [])
    }
  }
}
