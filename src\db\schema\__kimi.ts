// schema.ts
import { sql } from "drizzle-orm"
import { pgTable, text, bigint, integer, timestamp, boolean, index, varchar, uniqueIndex } from "drizzle-orm/pg-core"
import { walletSchema } from "./wallet"

// =====================================================
//  CONFIG  – satu baris = satu strategi / bot
// =====================================================
export const config = pgTable(
  `config`,
  {
    id: varchar({ length: 25 }).notNull().primaryKey(), // cuid, identitas strategi
    walletId: varchar({ length: 25 })
      .references(() => walletSchema.id, { onDelete: `cascade` })
      .notNull(), // wallet pemilik strategi
    name: varchar({ length: 10 }).notNull(), // nama strategi (bebas)
    tokenIn: varchar({ length: 50 }).notNull(), // token yg dikeluarkan (modal)
    tokenOut: varchar({ length: 50 }).notNull(), // token yg dibeli / dijual
    initAmount: bigint({ mode: `bigint` }).notNull(), // jumlah modal awal (tokenIn)
    rebuyBps: integer().notNull(), // % rebuy  (basis-point)
    sellBps: integer().notNull(), // % profit (basis-point)
    maxPendingSell: integer().notNull(), // maks order SELL pending
    active: boolean().notNull().default(true), // true = bot jalan, false = stop
    lastPrice: bigint({ mode: `bigint` }), // harga tokenOut terakhir di swap
    lastQty: bigint({ mode: `bigint` }), // jumlah tokenOut terakhir di swap
    createdAt: timestamp().defaultNow(), // waktu strategi dibuat
    updatedAt: timestamp().defaultNow() // // waktu strategi terakhir diubah
  },
  (t) => [
    uniqueIndex(`uniq_wallet_token`).on(t.walletId, t.tokenOut), // 1 wallet 1 strategi per tokenOut
    index(`idx_active`).on(t.active) // cepat filter strategi aktif
  ]
)

// =====================================================
//  ORDER  – semua order yg dikirim ke DEX
// =====================================================
export const order = pgTable(
  `order`,
  {
    id: varchar({ length: 25 }).notNull().primaryKey(), // cuid unik per order
    configId: varchar({ length: 25 })
      .references(() => config.id, { onDelete: `cascade` })
      .notNull(), // strategi mana yg membuat order
    hash: varchar({ length: 128 }).notNull().unique(), // txhash offchain dan txhash on-chain (updateable)
    chain: text({ enum: [`ethereum`, `solana`] }).notNull(), // blockchain yg dipakai
    side: text({ enum: [`buy`, `sell`] }).notNull(), // arah order
    type: text({ enum: [`init_buy`, `rebuy_up`, `rebuy_down`, `sell`] }).notNull(), // alasan pembuatan order
    price: bigint({ mode: `bigint` }).notNull(), // harga limit tokenOut saat order dibuat
    qty: bigint({ mode: `bigint` }).notNull(), // jumlah tokenOut yg diminta
    filled: bigint({ mode: `bigint` }).default(0n), // sudah terisi berapa
    status: text({ enum: [`pending`, `success`, `failed`] })
      .notNull()
      .default(`pending`), // status di DEX
    createdAt: timestamp().defaultNow(), // waktu order dibuat
    updatedAt: timestamp().defaultNow() // waktu terakhir berubah
  },
  (t) => [
    index(`idx_cfg_sell_pending`)
      .on(t.configId)
      .where(sql`side = 'sell' and status = 'pending'`), // cepat hitung pending sell
    index(`idx_cfg_pending`)
      .on(t.configId)
      .where(sql`status = 'pending'`) // cepat hitung semua pending
  ]
)
