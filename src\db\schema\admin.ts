import { index, integer, pgTable, timestamp, varchar } from "drizzle-orm/pg-core"
import { userSchema } from "./user"

export const adminSchema = pgTable(
  `admin`,
  {
    id: integer()
      .primaryKey()
      .references(() => userSchema.id, { onDelete: `cascade` }), // Reference to users table
    role: varchar({ length: 7 }).notNull().default(`support`), // Admin role (support, super)
    updatedAt: timestamp().defaultNow(),
    createdAt: timestamp().defaultNow()
  },
  (table) => [
    // Add index on id for faster searches
    index(`admin_id_idx`).on(table.id),
    // Add index on role for role-based filtering
    index(`admin_role_idx`).on(table.role)
  ]
)
