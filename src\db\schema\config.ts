import { pgTable, varchar, bigint, timestamp, text } from "drizzle-orm/pg-core"
import { type InferInsertModel, type InferSelectModel } from "drizzle-orm"
import { walletSchema } from "./wallet"

export type TypeConfigRecord = InferSelectModel<typeof configSchema>
export type TypeConfigInsert = InferInsertModel<typeof configSchema>
export const configSchema = pgTable(`config`, {
  id: varchar({ length: 25 }).notNull().primaryKey(), // config id create with cuid
  walletId: varchar({ length: 25 }) // wallet id as owner this config
    .notNull()
    .references(() => walletSchema.id, { onDelete: `cascade` }),
  name: varchar({ length: 10 }).notNull(), // config name
  initAmountIn: bigint({ mode: `bigint` }).notNull(), // jumlah awal token yang dikirim
  tokenIn: varchar({ length: 50 }).notNull(), // token yang dikirim
  tokenOut: varchar({ length: 50 }).notNull(), // token yang diterima
  rebuyBps: bigint({ mode: `bigint` }).notNull(), // % rebuy  (basis-point)
  sellBps: bigint({ mode: `bigint` }).notNull(), // % profit (basis-point)
  maxPendingSell: bigint({ mode: `bigint` }).notNull(), // max count pending sell
  status: text({ enum: [`inactive`, `active`] })
    .notNull()
    .default(`inactive`),
  createdAt: timestamp().defaultNow(),
  updatedAt: timestamp().defaultNow()
})
