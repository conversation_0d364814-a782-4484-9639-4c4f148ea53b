import { pgTable, varchar, bigint, timestamp, smallint, bigserial, text } from "drizzle-orm/pg-core"
import { sql, type InferInsertModel, type InferSelectModel } from "drizzle-orm"
import { configSchema } from "./config"

export type TypeMonitorRecord = InferSelectModel<typeof monitorSchema>
export type TypeMonitorInsert = InferInsertModel<typeof monitorSchema>
export const monitorSchema = pgTable(`monitor`, {
  id: bigserial({ mode: `bigint` }).notNull().primaryKey(), // just increment id
  configId: varchar({ length: 25 }) // wallet id as owner this config
    .notNull()
    .references(() => configSchema.id, { onDelete: `cascade` }),
  maxPendingSell: bigint({ mode: `bigint` }) // max count pending sell
    .notNull(),
  status: text({ enum: [`open`, `close`] }) // status monitor
    .notNull()
    .default(`open`),
  pendingSell: bigint({ mode: `bigint` }).notNull().default(0n), // current count pending sell
  totalAmountIn: bigint({ mode: `bigint` }).notNull(), // jumlah token yang dikirim
  totalAmountOut: bigint({ mode: `bigint` }).notNull(), // jumlah token yang diterima.
  updatedAt: timestamp().defaultNow(),
  createdAt: timestamp().defaultNow()
})
