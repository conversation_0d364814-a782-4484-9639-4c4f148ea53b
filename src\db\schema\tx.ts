import { type InferSelectModel, type InferInsertModel } from "drizzle-orm"
import { pgTable, varchar, bigint, timestamp, text, bigserial } from "drizzle-orm/pg-core"
import { configSchema } from "./config"

export type TypeTxRecord = InferSelectModel<typeof txSchema>
export type TypeTxInsert = InferInsertModel<typeof txSchema>
export const txSchema = pgTable(`tx`, {
  id: bigserial({ mode: `bigint` }).notNull().primaryKey(), // just increment id
  configId: varchar({ length: 25 }) // config Id from schemaConfig.id
    .notNull()
    .references(() => configSchema.id, { onDelete: `cascade` }),
  chain: varchar({ length: 10 }).notNull(), // example: ethereum, solana
  hash: varchar({ length: 128 }).notNull().unique(), // hash transaction langsung dari blockchain atau local preprocess statuspending
  from: varchar({ length: 50 }).notNull(), // sender transaction (wallet)
  to: varchar({ length: 50 }).notNull(), // receiver transaction (smartcontract)
  type: text({ enum: [`buy`, `sell`, `buy_up`, `buy_down`] }).notNull(), // alasan pembuatan tx. buy: membeli harga market | sell: menjual harga market | buy_up: membeli karena harga naik (acuan) | buy_down: membeli karena harga turun (acuan)
  amountIn: bigint({ mode: `bigint` }).notNull(), // jumlah token yang dikirim
  amountOut: bigint({ mode: `bigint` }).notNull(), // jumlah token yang diterima.
  slippage: bigint({ mode: `bigint` }).notNull(), // slippage yang di set dengan BASIS
  tokenIn: varchar({ length: 50 }).notNull(), // token yang dikirim
  tokenOut: varchar({ length: 50 }).notNull(), // token yang diterima
  status: text({ enum: [`pending`, `success`, `failed`] }) // status transaction langsung dari blockchain
    .notNull()
    .default(`pending`),
  updatedAt: timestamp().defaultNow(),
  createdAt: timestamp().defaultNow()
})

// sebenarnya untuk status open, canceled di schema "order" tidak harus di catat di table tersebut, jadi logika saya seperti di bawah, karena rebuyBps tidak boleh lebih dari sellBps. (koreksi jika salah) :

// const config = {} as any // contoh dari db.
// const priceNow = 2n * 10n ** 18n
// const rebuyBps = config.rebuyBps // 10%
// const sellBps = config.sellBps // 20%
// const BASIS = 10_000n
// const calcSell = priceNow + (priceNow * sellBps) / BASIS
// const calcRebuyUp = priceNow + (priceNow * rebuyBps) / BASIS
// const calcRebuyDown = priceNow - (priceNow * rebuyBps) / BASIS
// if (config.lastPrice >= calcSell) {
//   // profit (harga naik ke level sell)
//   // catat ke table "order" dengan config.side=sell dan config.type = sell dan config.status = filled
// } else if (config.lastPrice >= calcRebuyUp) {
//   // rebuy up (harga naik ke level rebuy up)
//   // catat ke table "order" dengan config.side=buy dan config.type = up dan config.status = filled
// } else if (config.lastPrice <= calcRebuyDown) {
//   // rebuy down (harga turun ke level rebuy down)
//   // catat ke table "order" dengan config.side=buy dan config.type = down dan config.status = filled
// } else {
//   // do nothing
// }
