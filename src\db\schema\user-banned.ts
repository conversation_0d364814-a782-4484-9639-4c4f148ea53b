import { boolean, index, integer, pgTable, timestamp, varchar, text } from "drizzle-orm/pg-core"
import type { InferSelectModel } from "drizzle-orm"
import { userSchema } from "./user"
import { adminSchema } from "./admin"

export type TypeUserBannedRecord = InferSelectModel<typeof userBannedSchema>
export type TypeUserBannedInsert = InferSelectModel<typeof userBannedSchema>
export const userBannedSchema = pgTable(
  `user_banned`,
  {
    id: integer().primaryKey().generatedAlwaysAsIdentity(), // Auto-increment primary key
    userId: integer()
      .notNull()
      .references(() => userSchema.id), // Foreign key to users table
    banId: varchar({ length: 32 }).notNull().unique(), // Unique ban identifier (e.g., "BAN-2025-001")
    reason: varchar({ length: 500 }).notNull(), // Reason for the ban
    description: text(), // Detailed description of the violation (optional)
    bannedBy: integer().references(() => adminSchema.id), // Admin user who issued the ban
    isActive: boolean().notNull().default(true), // Whether the ban is currently active
    isPermanent: boolean().notNull().default(false), // Whether this is a permanent ban
    expiresAt: timestamp(), // When the ban expires (null for permanent bans)
    unbanReason: varchar({ length: 500 }), // Reason for unbanning (if applicable)
    unbannedBy: integer().references(() => adminSchema.id), // Admin who lifted the ban
    unbannedAt: timestamp(), // When the ban was lifted
    updatedAt: timestamp().defaultNow(),
    createdAt: timestamp().defaultNow()
  },
  (table) => [
    // Add index on userId for faster user ban lookups
    index(`user_banned_user_id_idx`).on(table.userId),
    // Add index on banId for unique ban identification
    index(`user_banned_ban_id_idx`).on(table.banId),
    // Add index on isActive for filtering active bans
    index(`user_banned_is_active_idx`).on(table.isActive),
    // Add index on isPermanent for filtering permanent vs temporary bans
    index(`user_banned_is_permanent_idx`).on(table.isPermanent),
    // Add index on expiresAt for cleanup and expiry checks
    index(`user_banned_expires_at_idx`).on(table.expiresAt),
    // Add index on createdAt for chronological sorting
    index(`user_banned_created_at_idx`).on(table.createdAt)
  ]
)
