import type { <PERSON><PERSON> } from "../handler"
import { AdminModel } from "../db/models/admin"
import { AdminAccessDeniedPage } from "../pages/admin-access-denied"
import { GeneralErrorPage } from "../pages/general-error"

export class AdminGuard {
  private name = `AdminGuard`
  private generalErrorPage: GeneralErrorPage
  private adminAccessDeniedPage: AdminAccessDeniedPage
  constructor(private handler: Handler) {
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.adminAccessDeniedPage = new AdminAccessDeniedPage(this.handler)
  }

  async ensureValid() {
    try {
      const admin = await AdminModel.getById(this.handler.userId)
      if (admin === null) {
        await this.adminAccessDeniedPage.show()
        return null
      }
      return admin
    } catch (err) {
      await this.generalErrorPage.show(this.name, err)
      return null
    }
  }
}
