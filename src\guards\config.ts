import type { <PERSON><PERSON> } from "../handler"
import { ConfigModel } from "../db/models/config"
import { ConfigDetailEmptyPage } from "../pages/config-detail-empty"
import { ConfigEmptyPage } from "../pages/config-empty"
import { GeneralErrorPage } from "../pages/general-error"

export class ConfigGuard {
  private name = `ConfigGuard`
  private configDetailEmptyPage: ConfigDetailEmptyPage
  private configEmptyPage: ConfigEmptyPage
  private generalErrorPage: GeneralErrorPage
  constructor(private handler: Handler) {
    this.configDetailEmptyPage = new ConfigDetailEmptyPage(this.handler)
    this.configEmptyPage = new ConfigEmptyPage(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
  }

  async ensureExists(configName: string) {
    try {
      const config = await ConfigModel.getByNameForUser(configName, this.handler.userId)
      if (config === null) {
        await this.configDetailEmptyPage.show()
        return null
      }

      return config
    } catch (err) {
      await this.generalErrorPage.show(this.name, err)
      return null
    }
  }

  async ensureExistAll(limit: number = 50) {
    try {
      const config = await ConfigModel.getAllForUser(this.handler.userId, limit)
      if (config.length === 0) {
        await this.configEmptyPage.show()
        return null
      }

      return config
    } catch (err) {
      await this.generalErrorPage.show(this.name, err)
      return null
    }
  }
}
