import type { <PERSON><PERSON> } from "../handler"
import { UserModel } from "../db/models/user"
import { GeneralErrorPage } from "../pages/general-error"
import { UserBannedPage } from "../pages/user-banned"
import { UserCreateFailedPage } from "../pages/user-create-failed"
import { UserCreateSuccessPage } from "../pages/user-create-success"

export class UserGuard {
  private name = `UserGuard`
  private userCreateSuccessPage: UserCreateSuccessPage
  private userCreateFailedPage: UserCreateFailedPage
  private userBannedPage: UserBannedPage
  private generalErrorPage: GeneralErrorPage
  constructor(private handler: Handler) {
    this.userCreateSuccessPage = new UserCreateSuccessPage(this.handler)
    this.userCreateFailedPage = new UserCreateFailedPage(this.handler)
    this.userBannedPage = new UserBannedPage(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
  }

  async ensureValid() {
    try {
      const user = await UserModel.getById(this.handler.userId)
      if (user === null) {
        const { userId, userName, fullName } = this.handler
        const create = await UserModel.create(userId, userName, fullName)
        if (!create) {
          await this.userCreateFailedPage.show()
          return null
        }
        await this.userCreateSuccessPage.show()
        return null
      } else if (user.isActive === false) {
        await this.userBannedPage.show()
        return null
      }
      return user
    } catch (err) {
      await this.generalErrorPage.show(this.name, err)
      return null
    }
  }
}
