import { type Context, InlineKeyboard } from "grammy"
import { log } from "./utils/log"
import { Content } from "./utils/content"
import { Keyv } from "keyv"

type TypeSessionData = { method: string; params: any }
const content = new Content()
const cacheUser = new Keyv({ store: new Map(), ttl: 24 * 60 * 60 * 1000 }) // ttl = 24jam
const cacheKeyboard = new Map() // ttl = lifetime
const cacheCallbackData = new Map() // !!!!!! DONT USE THIS FEATURE FUTURE !!!!!!. ttl = lifetime

/**
 * Handler class containing core bot functionality
 * Provides session management, keyboard utilities, and base message handling
 */
export class Handler {
  passwordWallet = process.env.PASSWORD_WALLET as string
  userId: number
  userName: string
  fullName: string
  msgText: string
  callbackData: string
  get callbackDataParams() {
    return this.callbackData.split(`:`).pop() as string
  }
  constructor(private ctx: Context) {
    this.userId = ctx.from?.id as number
    this.userName = ctx.from?.username as string
    this.fullName = `${ctx.from?.first_name}${ctx.from?.last_name}`
    this.msgText = ctx.message?.text || ``
    this.callbackData = ctx.callbackQuery?.data as string
  }

  /**
   * Store session data for a user
   * @param sessionData Session data object containing state and temporary data
   */
  async sessionSet(sessionData: TypeSessionData): Promise<void> {
    try {
      await cacheUser.set(`session:${this.userId}`, sessionData)
    } catch (error) {
      log.error(`${Handler.name}:sessionSet: ${error}`)
    }
  }

  /**
   * Retrieve session data for a user
   * @returns Session data object or null if no active session
   */
  async sessionGet(): Promise<TypeSessionData | null> {
    try {
      return (await cacheUser.get(`session:${this.userId}`)) || null
    } catch (error) {
      log.error(`${Handler.name}:sessionGet: ${error}`)
      return null
    }
  }

  /**
   * Clear session data when operation completes
   */
  async sessionDelete(): Promise<void> {
    try {
      await cacheUser.delete(`session:${this.userId}`)
    } catch (error) {
      log.error(`${Handler.name}:sessionDelete: ${error}`)
    }
  }

  /**
   * Send message with inline keyboard and track state
   * @param contentKey Content key to retrieve from content system
   * @param keyboard Optional Inline keyboard markup
   * @param data Optional data object for variable replacement
   * @param options Optional reply options
   * @returns Promise from ctx.reply()
   */
  async replyMsg(contentKey: string, keyboard?: InlineKeyboard, data?: Record<string, any>, options?: any) {
    try {
      return await this.ctx.reply(content.get(contentKey, data), {
        parse_mode: `HTML`,
        reply_markup: keyboard,
        ...options
      })
    } catch (_) {
      return null
    }
  }

  /**
   * Update existing inline keyboard or delete if update fails
   * @param contentKey Content key for new message text
   * @param keyboard Optional inline keyboard markup
   * @param data Optional data object for variable replacement
   * @param options Optional reply options
   * @returns Promise indicating success
   */
  async updateMsg(contentKey: string, keyboard?: InlineKeyboard, data?: Record<string, any>, options?: any) {
    try {
      await this.ctx.editMessageText(content.get(contentKey, data), {
        parse_mode: `HTML`,
        reply_markup: keyboard,
        ...options
      })
    } catch (_) {
      await this.replyMsg(contentKey, keyboard, data, options)
    }
  }

  /**
   * Delete message and inlineKeyboard
   * @returns Promise indicating success
   */
  async deleteMsg(): Promise<void> {
    try {
      await this.ctx.deleteMessage()
    } catch (_) {
      return
    }
  }

  /**
   * Answer callback query
   * @param options Options params from ctx.answerCallbackQuery
   */
  async answerCbQuery(options?: any) {
    try {
      await this.ctx.answerCallbackQuery(options)
    } catch (_) {
      return
    }
  }

  /**
   * Create inline keyboard with caching
   * @param key Key store keyboard
   * @param fallback When keyboard undefined will call fallback and append to keyboard
   * @returns Inline keyboard markup
   */
  cacheKeyboard(key: string, fallback: () => InlineKeyboard) {
    let keyboard: ReturnType<typeof fallback> = cacheKeyboard.get(key)
    if (keyboard === undefined) {
      keyboard = fallback()
      cacheKeyboard.set(key, keyboard)
    }
    return keyboard
  }

  /**
   * !!!!!! DONT USE THIS FEATURE FUTURE !!!!!!. is function to make callback_data to short 16bytes
   * @param str
   * @param isDynamic
   * @returns
   */
  createCallbackData(str: string, isDynamic = false) {
    let data = cacheCallbackData.get(str)
    if (data === undefined) {
      function crc64(val: string) {
        // Just mockup
        if (val) {
          return `d30ef175beca118d`
        }
      }
      data = `${crc64(str)}${isDynamic ? `:` : ``}`
      cacheCallbackData.set(str, data)
    }
    return data
  }
}
