import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class AdminAccessDeniedPage {
  private name = Name.adminAccessDenied
  constructor(private handler: Hand<PERSON>) {}

  createKeyboard() {
    return new InlineKeyboard().text(`≡ Home`, Name.start)
  }

  async show() {
    const keyboard = this.createKeyboard()
    await this.handler.updateMsg(this.name, keyboard)
  }
}
