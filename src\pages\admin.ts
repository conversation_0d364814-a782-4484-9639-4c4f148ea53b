import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { AdminGuard } from "../guards/admin"
import { Name } from "../name"

export class AdminPage {
  private name = Name.admin
  private adminGuard: AdminGuard
  constructor(private handler: <PERSON><PERSON>) {
    this.adminGuard = new AdminGuard(this.handler)
  }

  createKeyboard() {
    return new InlineKeyboard().text(`≡ Home`, Name.start)
  }

  async show() {
    const admin = await this.adminGuard.ensureValid()
    if (admin === null) {
      return
    }

    const keyboard = this.createKeyboard()
    await this.handler.updateMsg(this.name, keyboard)
  }
}
