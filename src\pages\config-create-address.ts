import type { <PERSON><PERSON> } from "../handler"
import { InlineKeyboard } from "grammy"
import { GeneralErrorPage } from "./general-error"
import { BlockchainWallet } from "../blockchain/wallet"
import { ConfigCreateWarnInvalidAddressPage } from "./config-create-warn-invalid-address"
import { ConfigCreateAmountPage } from "./config-create-amount"
import { WalletGuard } from "../guards/wallet"
import { Name } from "../name"

export class ConfigCreateAddressPage {
  private name = Name.configCreateAddress
  private walletGuard: WalletGuard
  private generalErrorPage: GeneralErrorPage
  private configCreateAmountPage: ConfigCreateAmountPage
  private configCreateWarnInvalidAddressPage: ConfigCreateWarnInvalidAddressPage
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.configCreateAmountPage = new ConfigCreateAmountPage(this.handler)
    this.configCreateWarnInvalidAddressPage = new ConfigCreateWarnInvalidAddressPage(this.handler)
  }

  createKeyboard(walletName: string) {
    return new InlineKeyboard().text(`× Cancel`, Name.config).text(`🔄 Repeat`, `${Name.configCreateName}:${walletName}`)
  }

  async show(sessionParams: any) {
    const { walletName } = sessionParams
    await this.handler.sessionSet({
      method: `it-${this.name}`,
      params: sessionParams
    })

    const keyboard = this.createKeyboard(walletName)
    await this.handler.updateMsg(this.name, keyboard)
  }

  async input(sessionParams: Record<any, any>, input: string) {
    try {
      const { walletName, walletChain } = sessionParams
      const isAddress = BlockchainWallet.get(walletChain).isValidAddress(input)
      if (!isAddress) {
        await this.configCreateWarnInvalidAddressPage.show(walletName)
        return
      }

      await this.configCreateAmountPage.show({ ...sessionParams, inputAddress: input })
    } catch (error) {
      await this.generalErrorPage.show(this.name, error)
    }
  }
}
