import type { <PERSON><PERSON> } from "../handler"
import { InlineKeyboard } from "grammy"
import { GeneralErrorPage } from "./general-error"
import { ConfigCreateRebuyPage } from "./config-create-rebuy"
import { ConfigCreateWarnInvalidAmountPage } from "./config-create-warn-invalid-amount"
import { Name } from "../name"

export class ConfigCreateAmountPage {
  private name = Name.configCreateAmount
  private generalErrorPage: GeneralErrorPage
  private configCreateRebuyPage: ConfigCreateRebuyPage
  private configCreateWarnInvalidAmountPage: ConfigCreateWarnInvalidAmountPage
  constructor(private handler: Handler) {
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.configCreateRebuyPage = new ConfigCreateRebuyPage(this.handler)
    this.configCreateWarnInvalidAmountPage = new ConfigCreateWarnInvalidAmountPage(this.handler)
  }

  createKeyboard(walletName: string) {
    return new InlineKeyboard().text(`× Cancel`, Name.config).text(`🔄 Repeat`, `${Name.configCreateName}:${walletName}`)
  }

  async show(sessionParams: any) {
    const { walletName } = sessionParams
    await this.handler.sessionSet({
      method: `it-${this.name}`,
      params: sessionParams
    })

    const keyboard = this.createKeyboard(walletName)
    await this.handler.updateMsg(this.name, keyboard)
  }

  async input(sessionParams: any, input: string) {
    try {
      const { walletName } = sessionParams
      if (isNaN(input as any) || Number(input) <= 0) {
        await this.configCreateWarnInvalidAmountPage.show(walletName)
        return
      }

      await this.configCreateRebuyPage.show({ ...sessionParams, inputAmount: input })
    } catch (error) {
      await this.generalErrorPage.show(this.name, error)
    }
  }
}
