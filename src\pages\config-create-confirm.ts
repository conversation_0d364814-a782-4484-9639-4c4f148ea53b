import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { WalletGuard } from "../guards/wallet"
import { Name } from "../name"

export class ConfigCreateConfirmPage {
  private name = Name.configCreateConfirm
  private walletGuard: WalletGuard
  constructor(private handler: <PERSON><PERSON>) {
    this.walletGuard = new WalletGuard(this.handler)
  }

  createKeyboard(walletName: string) {
    return (
      new InlineKeyboard()
        //
        .text(`✅ Yes, Create`, Name.configCreateExecute)
        .row()
        .text(`× Cancel`, Name.config)
        .text(`🔄 Repeat`, `${Name.configCreateName}:${walletName}`)
    )
  }

  async show(sessionParams: any) {
    const { walletName, walletChain } = sessionParams
    const { chainSymbol } = BlockchainConfig.get(walletChain)
    const wallet = await this.walletGuard.ensureExists(walletName)
    if (wallet === null) {
      return
    }

    // Just change method name, why use session? because callback_data can handle more than 64byte so for soluditon we use session
    await this.handler.sessionSet({
      method: `ck-${Name.configCreateExecute}`,
      params: sessionParams
    })

    const keyboard = this.createKeyboard(walletName)
    await this.handler.updateMsg(this.name, keyboard, {
      ...sessionParams,
      chainSymbol
    })
  }
}
