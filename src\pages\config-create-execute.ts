import type { Hand<PERSON> } from "../handler"
import { GeneralErrorSessionPage } from "./general-error-session"
import { ConfigCreateSuccessPage } from "./config-create-success"
import { GeneralErrorPage } from "./general-error"
import { WalletGuard } from "../guards/wallet"
import { Name } from "../name"
import { ConfigModel } from "../db/models/config"
import { ConfigCreateFailedPage } from "./config-create-failed"
import { BlockchainUtils } from "../blockchain/utils"
import { BlockchainConfig } from "../blockchain/config"

export class ConfigCreateExecutePage {
  private name = Name.configCreateExecute
  private walletGuard: WalletGuard
  private generalErrorPage: GeneralErrorPage
  private generalErrorSessionPage: GeneralErrorSessionPage
  private configCreateSuccessPage: ConfigCreateSuccessPage
  private configCreateFailedPage: ConfigCreateFailedPage
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.generalErrorSessionPage = new GeneralErrorSessionPage(this.handler)
    this.configCreateSuccessPage = new ConfigCreateSuccessPage(this.handler)
    this.configCreateFailedPage = new ConfigCreateFailedPage(this.handler)
  }

  async show() {
    try {
      const sessionData = await this.handler.sessionGet()
      if (!sessionData || sessionData.method !== `ck-${this.name}`) {
        await this.generalErrorSessionPage.show()
        return
      }

      // Clear the session
      await this.handler.sessionDelete()

      const sessionParams = sessionData.params
      const { walletName, inputName, inputAddress, inputAmount, inputRebuy, inputSell, inputMaxPendingSell } = sessionParams
      const wallet = await this.walletGuard.ensureExists(walletName)
      if (wallet === null) {
        return
      }

      const { chainDecimals } = BlockchainConfig.get(wallet.chain)
      const create = await ConfigModel.create({
        walletId: wallet.id,
        name: inputName,
        amount: BlockchainUtils.parseUnits(inputAmount, chainDecimals), // for now only native coin support, future will use token and get decimals with special method
        path: [`native`, inputAddress],
        percentRebuy: Number(inputRebuy),
        percentSell: Number(inputSell),
        maxPendingSell: Number(inputMaxPendingSell)
      })

      if (create) {
        await this.configCreateSuccessPage.show(sessionParams, wallet)
      } else {
        await this.configCreateFailedPage.show(sessionParams, wallet)
      }
    } catch (error) {
      await this.generalErrorPage.show(this.name, error)
    }
  }
}
