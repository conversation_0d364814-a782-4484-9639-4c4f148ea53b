import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { Name } from "../name"

export class ConfigCreateFailedPage {
  private name = Name.configCreateFailed
  constructor(private handler: Handler) {}

  createKeyboard(walletName: string) {
    return (
      new InlineKeyboard()
        //
        .text(`🔄 Repeat`, `${Name.configCreateName}:${walletName}`)
        .row()
        .text(`× Cancel`, Name.config)
        .text(`≡ Home`, Name.start)
    )
  }

  async show(sessionParams: any, wallet: any) {
    await this.handler.sessionDelete()
    const { inputAddress, inputAmount, inputRebuy, inputSell, inputMaxPendingSell } = sessionParams
    const { chainDisplayName, chainSymbol } = BlockchainConfig.get(wallet.chain)

    const keyboard = this.createKeyboard(wallet.id)
    await this.handler.updateMsg(this.name, keyboard, {
      walletName: wallet.name,
      walletAddress: wallet.address,
      walletPrivateKey: wallet.privateKey,
      chainName: chainDisplayName,
      chainSymbol,
      inputAddress,
      inputAmount,
      inputRebuy,
      inputSell,
      inputMaxPendingSell
    })
  }
}
