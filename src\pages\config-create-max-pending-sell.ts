import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { ConfigCreateWarnInvalidAmountPage } from "./config-create-warn-invalid-amount"
import { ConfigCreateConfirmPage } from "./config-create-confirm"
import { GeneralErrorPage } from "./general-error"
import { Name } from "../name"

export class ConfigCreateMaxPendingSellPage {
  private name = Name.configCreateMaxPendingSell
  private generalErrorPage: GeneralErrorPage
  private configCreateConfirmPage: ConfigCreateConfirmPage
  private configCreateWarnInvalidAmountPage: ConfigCreateWarnInvalidAmountPage
  constructor(private handler: Handler) {
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.configCreateConfirmPage = new ConfigCreateConfirmPage(this.handler)
    this.configCreateWarnInvalidAmountPage = new ConfigCreateWarnInvalidAmountPage(this.handler)
  }

  createKeyboard(walletName: string) {
    return new InlineKeyboard().text(`× Cancel`, Name.config).text(`🔄 Repeat`, `${Name.configCreateName}:${walletName}`)
  }

  async show(sessionParams: any) {
    const { walletName } = sessionParams
    await this.handler.sessionSet({
      method: `it-${this.name}`,
      params: sessionParams
    })

    const keyboard = this.createKeyboard(walletName)
    await this.handler.updateMsg(this.name, keyboard)
  }

  async input(sessionParams: any, input: string) {
    try {
      const { walletName } = sessionParams
      if (isNaN(input as any) || Number(input) <= 0) {
        await this.configCreateWarnInvalidAmountPage.show(walletName)
        return
      }

      await this.configCreateConfirmPage.show({ ...sessionParams, inputMaxPendingSell: input })
    } catch (error) {
      await this.generalErrorPage.show(this.name, error)
    }
  }
}
