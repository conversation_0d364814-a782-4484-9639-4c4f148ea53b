import type { <PERSON><PERSON> } from "../handler"
import { InlineKeyboard } from "grammy"
import { GeneralErrorPage } from "./general-error"
import { BlockchainWallet } from "../blockchain/wallet"
import { ConfigCreateWarnInvalidAddressPage } from "./config-create-warn-invalid-address"
import { ConfigCreateAmountPage } from "./config-create-amount"
import { WalletGuard } from "../guards/wallet"
import { Name } from "../name"
import { ConfigModel } from "../db/models/config"
import { ConfigCreateWarnNameExistsPage } from "./config-create-warn-name-exists"
import { ConfigCreateWarnNameInvalidPage } from "./config-create-warn-name-invalid"
import { ConfigCreateAddressPage } from "./config-create-address"

export class ConfigCreateNamePage {
  private name = Name.configCreateName
  private walletGuard: WalletGuard
  private generalErrorPage: GeneralErrorPage
  private configCreateAddressPage: ConfigCreateAddressPage
  private configCreateWarnNameExistsPage: ConfigCreateWarnNameExistsPage
  private configCreateWarnNameInvalidPage: ConfigCreateWarnNameInvalidPage
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.configCreateAddressPage = new ConfigCreateAddressPage(this.handler)
    this.configCreateWarnNameExistsPage = new ConfigCreateWarnNameExistsPage(this.handler)
    this.configCreateWarnNameInvalidPage = new ConfigCreateWarnNameInvalidPage(this.handler)
  }

  createKeyboard() {
    return this.handler.cacheKeyboard(this.name, () => {
      return new InlineKeyboard().text(`× Cancel`, Name.config)
    })
  }

  async show() {
    const walletName = this.handler.callbackDataParams
    const wallet = await this.walletGuard.ensureExists(walletName)
    if (wallet === null) {
      return
    }

    await this.handler.sessionSet({
      method: `it-${this.name}`,
      params: { walletName, walletChain: wallet.chain }
    })

    const keyboard = this.createKeyboard()
    await this.handler.updateMsg(this.name, keyboard)
  }

  async input(sessionParams: Record<any, any>, input: string) {
    try {
      if (input.length > 10 || !/^[a-zA-Z0-9_/-]+$/.test(input)) {
        await this.configCreateWarnNameInvalidPage.show()
        return
      }

      // Check if config name already exists
      const existingConfig = await ConfigModel.getByNameForUser(input, this.handler.userId)
      if (existingConfig) {
        await this.configCreateWarnNameExistsPage.show()
        return
      }

      await this.configCreateAddressPage.show({ ...sessionParams, inputName: input })
    } catch (error) {
      await this.generalErrorPage.show(this.name, error)
    }
  }
}
