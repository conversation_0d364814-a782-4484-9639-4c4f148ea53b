import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { GeneralErrorPage } from "./general-error"
import { ConfigCreateSellPage } from "./config-create-sell"
import { ConfigCreateWarnInvalidAmountPage } from "./config-create-warn-invalid-amount"
import { Name } from "../name"

export class ConfigCreateRebuyPage {
  private name = Name.configCreateRebuy
  private generalErrorPage: GeneralErrorPage
  private configCreateSellPage: ConfigCreateSellPage
  private configCreateWarnInvalidAmountPage: ConfigCreateWarnInvalidAmountPage
  constructor(private handler: Handler) {
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.configCreateSellPage = new ConfigCreateSellPage(this.handler)
    this.configCreateWarnInvalidAmountPage = new ConfigCreateWarnInvalidAmountPage(this.handler)
  }

  createKeyboard(walletName: string) {
    return new InlineKeyboard().text(`× Cancel`, Name.config).text(`🔄 Repeat`, `${Name.configCreateName}:${walletName}`)
  }

  async show(sessionParams: any) {
    const { walletName } = sessionParams
    await this.handler.sessionSet({
      method: `it-${this.name}`,
      params: sessionParams
    })

    const keyboard = this.createKeyboard(walletName)
    await this.handler.updateMsg(this.name, keyboard)
  }

  async input(sessionParams: Record<any, any>, input: string) {
    try {
      const { walletName } = sessionParams
      if (isNaN(input as any) || Number(input) <= 0 || Number(input) > 100) {
        await this.configCreateWarnInvalidAmountPage.show(walletName)
        return
      }

      await this.configCreateSellPage.show({ ...sessionParams, inputRebuy: input })
    } catch (error) {
      await this.generalErrorPage.show(this.name, error)
    }
  }
}
