import { InlineKeyboard } from "grammy"
import type { Hand<PERSON> } from "../handler"
import { GeneralErrorPage } from "./general-error"
import { ConfigCreateWarnInvalidAmountPage } from "./config-create-warn-invalid-amount"
import { ConfigCreateMaxPendingSellPage } from "./config-create-max-pending-sell"
import { Name } from "../name"
import { ConfigCreateWarnInvalidSellPage } from "./config-create-warn-invalid-sell"

export class ConfigCreateSellPage {
  private name = Name.configCreateSell
  private generalErrorPage: GeneralErrorPage
  private configCreateWarnInvalidAmountPage: ConfigCreateWarnInvalidAmountPage
  private configCreateMaxPendingSellPage: ConfigCreateMaxPendingSellPage
  private configCreateWarnInvalidSellPage: ConfigCreateWarnInvalidSellPage
  constructor(private handler: Handler) {
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.configCreateWarnInvalidAmountPage = new ConfigCreateWarnInvalidAmountPage(this.handler)
    this.configCreateMaxPendingSellPage = new ConfigCreateMaxPendingSellPage(this.handler)
    this.configCreateWarnInvalidSellPage = new ConfigCreateWarnInvalidSellPage(this.handler)
  }

  createKeyboard(walletName: string) {
    return new InlineKeyboard().text(`× Cancel`, Name.config).text(`🔄 Repeat`, `${Name.configCreateName}:${walletName}`)
  }

  async show(sessionParams: any) {
    const { walletName } = sessionParams
    await this.handler.sessionSet({
      method: `it-${this.name}`,
      params: sessionParams
    })

    const keyboard = this.createKeyboard(walletName)
    await this.handler.updateMsg(this.name, keyboard)
  }

  async input(sessionParams: any, input: string) {
    try {
      const { walletName, inputRebuy } = sessionParams
      if (isNaN(input as any) || Number(input) <= 0) {
        await this.configCreateWarnInvalidAmountPage.show(walletName)
        return
      }

      if (Number(input) <= Number(inputRebuy)) {
        await this.configCreateWarnInvalidSellPage.show(walletName)
        return
      }

      await this.configCreateMaxPendingSellPage.show({ ...sessionParams, inputSell: input })
    } catch (error) {
      await this.generalErrorPage.show(this.name, error)
    }
  }
}
