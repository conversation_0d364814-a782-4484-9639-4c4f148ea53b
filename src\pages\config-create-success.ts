import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { Name } from "../name"

export class ConfigCreateSuccessPage {
  private name = Name.configCreateSuccess
  constructor(private handler: Hand<PERSON>) {}

  createKeyboard(configName: string) {
    return (
      new InlineKeyboard()
        //
        .text(`👁️ View Config`, `${Name.configDetail}:${configName}`)
        .text(`➕ Create Another`, Name.configCreate)
        .row()
        .text(`... Back`, Name.config)
        .text(`≡ Home`, Name.start)
    )
  }

  async show(sessionParams: any, wallet: any) {
    await this.handler.sessionDelete()
    const { inputName, inputAddress, inputAmount, inputRebuy, inputSell, inputMaxPendingSell } = sessionParams
    const { chainDisplayName, chainSymbol } = BlockchainConfig.get(wallet.chain)

    const keyboard = this.createKeyboard(inputName)
    await this.handler.updateMsg(this.name, keyboard, {
      walletName: wallet.name,
      walletAddress: wallet.address,
      walletPrivateKey: wallet.privateKey,
      chainName: chainDisplayName,
      chainSymbol,
      inputName,
      inputAddress,
      inputAmount,
      inputRebuy,
      inputSell,
      inputMaxPendingSell
    })
  }
}
