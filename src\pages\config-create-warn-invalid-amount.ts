import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class ConfigCreateWarnInvalidAmountPage {
  private name = Name.configCreateWarnInvalidAmount
  constructor(private handler: Handler) {}

  createKeyboard(walletName: string) {
    return new InlineKeyboard().text(`× Cancel`, Name.config).text(`🔄 Repeat`, `${Name.configCreateName}:${walletName}`)
  }

  async show(walletName: string) {
    const keyboard = this.createKeyboard(walletName)
    await this.handler.replyMsg(this.name, keyboard)
  }
}
