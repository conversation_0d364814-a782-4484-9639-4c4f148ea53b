import type { <PERSON><PERSON> } from "../handler"
import { ConfigModel } from "../db/models/config"
import { ConfigGuard } from "../guards/config"
import { Name } from "../name"
import { ConfigDetailDeleteFailedPage } from "./config-detail-delete-failed"
import { ConfigDetailDeleteSuccessPage } from "./config-detail-delete-success"
import { GeneralErrorPage } from "./general-error"

export class ConfigDetailDeleteExecutePage {
  private name = Name.configDetailDeleteExecute
  private configGuard: ConfigGuard
  private configDetailDeleteFailedPage: ConfigDetailDeleteFailedPage
  private configDetailDeleteSuccessPage: ConfigDetailDeleteSuccessPage
  private generalErrorPage: GeneralErrorPage
  constructor(private handler: Handler) {
    this.configGuard = new ConfigGuard(this.handler)
    this.configDetailDeleteFailedPage = new ConfigDetailDeleteFailedPage(this.handler)
    this.configDetailDeleteSuccessPage = new ConfigDetailDeleteSuccessPage(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
  }

  async show() {
    try {
      const configName = this.handler.callbackDataParams
      const config = await this.configGuard.ensureExists(configName)
      if (config === null) {
        return
      }

      const success = await ConfigModel.remove(config.id, config.walletId)
      if (success) {
        await this.configDetailDeleteSuccessPage.show()
      } else {
        await this.configDetailDeleteFailedPage.show()
      }
    } catch (err) {
      await this.generalErrorPage.show(this.name, err)
    }
  }
}
