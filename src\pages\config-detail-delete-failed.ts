import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class ConfigDetailDeleteFailedPage {
  private name = Name.configDetailDeleteFailed
  constructor(private handler: Handler) {}

  createKeyboard() {
    return this.handler.cacheKeyboard(this.name, () => {
      return new InlineKeyboard().text(`... Back`, Name.config).text(`≡ Home`, Name.start)
    })
  }

  async show() {
    const keyboard = this.createKeyboard()
    await this.handler.updateMsg(this.name, keyboard)
  }
}
