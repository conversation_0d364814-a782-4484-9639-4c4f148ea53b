import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { ConfigGuard } from "../guards/config"
import { WalletGuard } from "../guards/wallet"
import { Name } from "../name"

export class ConfigDetailDeletePage {
  private name = Name.configDetailDelete
  private configGuard: ConfigGuard
  private walletGuard: WalletGuard
  constructor(private handler: Handler) {
    this.configGuard = new ConfigGuard(this.handler)
    this.walletGuard = new WalletGuard(this.handler)
  }

  createKeyboard(configId: string) {
    return (
      new InlineKeyboard()
        //
        .text(`... Back`, `${Name.configDetail}:${configId}`)
        .text(`✅ Yes, Delete`, `${Name.configDetailDeleteExecute}:${configId}`)
    )
  }

  async show() {
    const configName = this.handler.callbackDataParams
    const config = await this.configGuard.ensureExists(configName)
    if (config === null) {
      return
    }

    const wallet = await this.walletGuard.ensureExistsWithId(config.walletId)
    if (wallet === null) {
      return
    }

    const { chainSymbol } = BlockchainConfig.get(wallet.chain as any)
    const keyboard = this.createKeyboard(configName)
    await this.handler.updateMsg(this.name, keyboard, {
      configName: config.name,
      walletName: wallet.name,
      chainSymbol
    })
  }
}
