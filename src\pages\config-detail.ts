import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { ConfigGuard } from "../guards/config"
import { WalletGuard } from "../guards/wallet"
import { Name } from "../name"
import { Time } from "../utils/time"
import { BlockchainUtils } from "../blockchain/utils"

export class ConfigDetailPage {
  private name = Name.configDetail
  private configGuard: ConfigGuard
  private walletGuard: WalletGuard
  constructor(private handler: Handler) {
    this.configGuard = new ConfigGuard(this.handler)
    this.walletGuard = new WalletGuard(this.handler)
  }

  createKeyboard(configName: string) {
    return (
      new InlineKeyboard()
        //
        .text(`🔄 Refresh`, `${this.name}:${configName}`)
        .text(`🗑️ Delete`, `${Name.configDetailDelete}:${configName}`)
        .row()
        .text(`... Back`, Name.config)
        .text(`≡ Home`, Name.start)
    )
  }

  async show() {
    const configName = this.handler.callbackDataParams
    const config = await this.configGuard.ensureExists(configName)
    if (config === null) {
      return
    }

    const wallet = await this.walletGuard.ensureExistsWithId(config.walletId)
    if (wallet === null) {
      return
    }

    const { chainSymbol, chainDecimals } = BlockchainConfig.get(wallet.chain as any)
    const keyboard = this.createKeyboard(configName)
    await this.handler.updateMsg(this.name, keyboard, {
      configName: config.name,
      configStatus: config.status,
      configCreatedAt: Time.format(config.createdAt),
      configUpdatedAt: Time.format(config.updatedAt),
      walletName: wallet.name,
      chainSymbol,
      configAmount: BlockchainUtils.formatUnits(config.amount, chainDecimals),
      configPercentRebuy: config.percentRebuy,
      configPercentSell: config.percentSell,
      configMaxPendingSell: config.maxPendingSell,
      configPathIn: config.path[0],
      configPathOut: config.path[1],
      timestamp: Time.nowUnix().toString()
    })
  }
}
