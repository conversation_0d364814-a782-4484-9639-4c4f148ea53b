import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { ConfigGuard } from "../guards/config"
import { Name } from "../name"

export class ConfigPage {
  private name = Name.config
  private configGuard: ConfigGuard
  constructor(private handler: <PERSON><PERSON>) {
    this.configGuard = new ConfigGuard(this.handler)
  }

  /**
   * Create config list keyboard with pagination
   * @param config Array of config objects
   * @param currentPage Current page number (0-based)
   * @param totalPages Total number of pages
   */
  createKeyboard(config: any[], currentPage: number, totalPages: number) {
    const keyboard = new InlineKeyboard()
    const prefixNext = Name.configDetail
    const prefixPagination = this.name

    // Add config buttons
    for (let index = 0; index < config.length; index++) {
      const { name } = config[index]
      const displayName = `${name}`

      keyboard.text(displayName, `${prefixNext}:${name}`).row()
    }

    // Add pagination if needed
    if (totalPages > 1) {
      keyboard.row()
      if (currentPage > 0) {
        keyboard.text(`⬅️ Previous`, `${prefixPagination}:${currentPage - 1}`)
      }
      keyboard.text(`${currentPage + 1}/${totalPages}`, `noop`)
      if (currentPage < totalPages - 1) {
        keyboard.text(`➡️ Next`, `${prefixPagination}:${currentPage + 1}`)
      }
    }

    // Add action buttons
    keyboard.row().text(`... Back`, Name.start).text(`➕ Create`, Name.configCreate)
    return keyboard
  }

  async show() {
    await this.handler.sessionDelete()
    const part = this.handler.callbackData.split(`:`)[1] as string // dont use callbackDataParams because this spesific
    const page = parseInt(part === undefined ? `0` : part)
    const config = await this.configGuard.ensureExistAll()
    if (config === null) {
      return
    }

    const configPerPage = 7
    const totalPages = Math.ceil(config.length / configPerPage)
    const currentPage = Math.max(0, Math.min(page, totalPages - 1))
    const startIndex = currentPage * configPerPage
    const endIndex = Math.min(startIndex + configPerPage, config.length)

    // Get config for current page
    const pageConfig = config.slice(startIndex, endIndex)

    const keyboard = this.createKeyboard(pageConfig, currentPage, totalPages)
    await this.handler.updateMsg(this.name, keyboard, {
      totalConfig: config.length,
      currentPage: currentPage + 1,
      totalPages,
      startIndex,
      configList: pageConfig
    })
  }
}
