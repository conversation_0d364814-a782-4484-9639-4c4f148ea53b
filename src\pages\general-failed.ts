import { InlineKeyboard } from "grammy"
import { log } from "../utils/log"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class GeneralFailedPage {
  private name = Name.generalFailed
  constructor(private handler: Handler) {}

  createKeyboard() {
    return this.handler.cacheKeyboard(this.name, () => {
      return new InlineKeyboard().text(`≡ Home`, Name.start)
    })
  }

  async show(location: string, message: any) {
    log.failed(`${location}: ${JSON.stringify(message)}`)
    await this.handler.sessionDelete()
    const keyboard = this.createKeyboard()
    await this.handler.updateMsg(this.name, keyboard)
  }
}
