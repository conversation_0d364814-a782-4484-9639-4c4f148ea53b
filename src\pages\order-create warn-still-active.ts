import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class OrderCreateWarnStillActivePage {
  private name = Name.orderCreateWarnStillActive
  constructor(private handler: Handler) {}

  createKeyboard() {
    return new InlineKeyboard().text(`... Back`, Name.order)
  }

  async show(configName: string) {
    const keyboard = this.createKeyboard()
    await this.handler.updateMsg(this.name, keyboard, {
      configName
    })
  }
}
