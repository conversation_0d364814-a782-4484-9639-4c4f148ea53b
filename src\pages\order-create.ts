import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"
import { ConfigGuard } from "../guards/config"
import { WalletGuard } from "../guards/wallet"
import { OrderCreateWarnStillActivePage } from "./order-create warn-still-active"
import { ConfigModel } from "../db/models/config"

export class OrderCreatePage {
  private name = Name.orderCreate
  private configGuard: ConfigGuard
  private walletGuard: WalletGuard
  private orderCreateWarnStillActivePage: OrderCreateWarnStillActivePage
  constructor(private handler: Handler) {
    this.configGuard = new ConfigGuard(this.handler)
    this.walletGuard = new WalletGuard(this.handler)
    this.orderCreateWarnStillActivePage = new OrderCreateWarnStillActivePage(this.handler)
  }

  createKeyboard() {
    return new InlineKeyboard().text(`... Back`, Name.order)
  }

  async show() {
    const configName = this.handler.callbackDataParams
    const config = await this.configGuard.ensureExists(configName)
    if (config === null) {
      return
    }

    const wallet = await this.walletGuard.ensureExistsWithId(config.walletId)
    if (wallet === null) {
      return
    }

    // still active = used. dont create
    if (config.status === 1) {
      await this.orderCreateWarnStillActivePage.show(configName)
      return
    }

    // change status to 1 = active
    await ConfigModel.updateStatus(config.id, 1)

    const keyboard = this.createKeyboard()
    await this.handler.updateMsg(this.name, keyboard)
  }
}
