import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class SettingPage {
  private name = Name.setting
  constructor(private handler: Hand<PERSON>) {}

  createKeyboard() {
    return this.handler.cacheKeyboard(this.name, () => {
      return new InlineKeyboard().text(`⚡ Priority`, `config-priority`).text(`⚖️ Slippage`, `config-slippage`).row().text(`🟢 Amount`, `config-amount`).text(`♻️ Reset`, `config-reset`).row().text(`... Back`, Name.start)
    })
  }

  async show() {
    const keyboard = this.createKeyboard()
    await this.handler.updateMsg(this.name, keyboard)
  }
}
