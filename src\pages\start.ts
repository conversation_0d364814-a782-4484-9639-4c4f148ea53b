import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class StartPage {
  private name = Name.start
  constructor(private handler: <PERSON><PERSON>) {}

  createKeyboard() {
    return this.handler.cacheKeyboard(this.name, () => {
      return (
        new InlineKeyboard()
          // force new line
          .text(`💼 Wallets`, Name.wallet)
          .text(`📊 Orders`, Name.order)
          .row()
          .text(`⚙️ Config`, Name.config)
          .text(`🛠️ Setting`, Name.setting)
          .row()
          .text(`🤝 Referral`, Name.referral)
          .text(`❓ Help`, Name.help)
          .row()
          .text(`🌐 Language`, Name.language)
      )
    })
  }

  async show() {
    await this.handler.sessionDelete()
    const keyboard = this.createKeyboard()
    await this.handler.updateMsg(this.name, keyboard)
  }
}
