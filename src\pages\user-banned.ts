import type { <PERSON><PERSON> } from "../handler"
import { InlineKeyboard } from "grammy"
import { AdminModel } from "../db/models/admin"
import { UserBannedModel } from "../db/models/user-banned"
import { GeneralErrorPage } from "./general-error"
import { GeneralFailedPage } from "./general-failed"
import { Time } from "../utils/time"
import { Name } from "../name"

export class UserBannedPage {
  private name = Name.userBanned
  private generalErrorPage: GeneralErrorPage
  private generalFailedPage: GeneralFailedPage
  constructor(private handler: Handler) {
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.generalFailedPage = new GeneralFailedPage(this.handler)
  }

  createKeyboard(usernameAdmin: string) {
    return new InlineKeyboard().url(`📞 Contact Support`, `https://t.me/${usernameAdmin}`)
  }

  async show() {
    try {
      const admin = (await AdminModel.getFirstByRole(`super`)) || ({} as any)
      const data = await UserBannedModel.getActiveBan(this.handler.userId)
      const keyboard = this.createKeyboard(admin.username)
      if (data === null) {
        await this.generalFailedPage.show(this.name, `Status user inactive but ban data is null`)
        return
      }

      // Calculate dynamic ban duration display
      let banDuration = `Permanent`
      let timeRemaining = ``
      if (!data.isPermanent && data.expiresAt) {
        const expiryDate = new Date(data.expiresAt)
        const now = new Date()
        const timeLeft = expiryDate.getTime() - now.getTime()
        if (timeLeft > 0) {
          const days = Math.floor(timeLeft / (1000 * 60 * 60 * 24))
          const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
          const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60))
          if (days > 0) {
            timeRemaining = `${days} day(s), ${hours} hour(s)`
          } else if (hours > 0) {
            timeRemaining = `${hours} hour(s), ${minutes} minute(s)`
          } else {
            timeRemaining = `${minutes} minute(s)`
          }

          banDuration = `Temporary (${timeRemaining} remaining)`
        } else {
          banDuration = `Expired (pending cleanup)`
        }
      }

      // Render template with formatted data
      await this.handler.updateMsg(this.name, keyboard, {
        reason: data.reason,
        banId: data.banId,
        banDuration,
        timeRemaining,
        banDate: data.createdAt ? Time.format(data.createdAt) : `Unknown`,
        expiresDate: data.expiresAt ? Time.format(data.expiresAt) : `Never`,
        isPermanent: data.isPermanent
      })
    } catch (error) {
      await this.generalErrorPage.show(this.name, error)
    }
  }
}
