import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class UserCreateFailedPage {
  private name = Name.userCreateFailed
  constructor(private handler: Handler) {}

  createKeyboard() {
    return this.handler.cacheKeyboard(this.name, () => {
      return new InlineKeyboard().text(`🔄 Try Again`, Name.start)
    })
  }

  async show() {
    const keyboard = this.createKeyboard()
    this.handler.updateMsg(this.name, keyboard)
  }
}
