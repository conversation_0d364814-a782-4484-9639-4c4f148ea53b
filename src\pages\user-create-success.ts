import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class UserCreateSuccessPage {
  private name = Name.userCreateSuccess
  constructor(private handler: Handler) {}

  createKeyboard() {
    return this.handler.cacheKeyboard(this.name, () => {
      return new InlineKeyboard().text(`≡ Home`, Name.start)
    })
  }

  async show() {
    const keyboard = this.createKeyboard()
    this.handler.updateMsg(this.name, keyboard)
  }
}
