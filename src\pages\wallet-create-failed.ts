import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class WalletCreateFailedPage {
  private name = Name.walletCreateFailed
  constructor(private handler: Handler) {}

  createKeyboard(chainName: string) {
    return this.handler.cacheKeyboard(`${this.name}:${chainName}`, () => {
      return new InlineKeyboard().text(`🔄 Try Again`, `${Name.walletCreate}:${chainName}`).text(`× Cancel`, Name.wallet)
    })
  }

  async show(chainName: string) {
    await this.handler.sessionDelete()
    const keyboard = this.createKeyboard(chainName)
    await this.handler.replyMsg(this.name, keyboard)
  }
}
