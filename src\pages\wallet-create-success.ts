import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { Name } from "../name"

export class WalletCreateSuccessPage {
  private name = Name.walletCreateSuccess
  constructor(private handler: Handler) {}

  createKeyboard(walletName: string, chainName: string) {
    return (
      new InlineKeyboard()
        //
        .text(`👁️ View Wallet`, `${Name.walletDetail}:${walletName}`)
        .text(`➕ Create Another`, `${Name.walletCreate}:${chainName}`)
        .row()
        .text(`... Back`, Name.wallet)
        .text(`≡ Home`, Name.start)
    )
  }

  async show(chainName: string, wallet: any) {
    await this.handler.sessionDelete()

    const { chainDisplayName } = BlockchainConfig.get(chainName as any)
    const keyboard = this.createKeyboard(wallet.name, chainName)
    await this.handler.replyMsg(this.name, keyboard, {
      walletName: wallet.name,
      walletAddress: wallet.address,
      chainName: chainDisplayName
    })
  }
}
