import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { Name } from "../name"

export class WalletCreateWarnDuplicateAddressPage {
  private name = Name.walletCreateWarnDuplicateAddress
  constructor(private handler: Handler) {}

  createKeyboard() {
    return this.handler.cacheKeyboard(this.name, () => {
      return new InlineKeyboard().text(`× Cancel`, Name.wallet)
    })
  }

  async show(chainName: string, walletAddress: string) {
    const { chainDisplayName } = BlockchainConfig.get(chainName as any)
    const keyboard = this.createKeyboard()
    await this.handler.replyMsg(this.name, keyboard, {
      chainName: chainDisplayName,
      walletAddress
    })
  }
}
