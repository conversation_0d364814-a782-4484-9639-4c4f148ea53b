import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class WalletCreateWarnNameExistsPage {
  private name = Name.walletCreateWarnNameExists
  constructor(private handler: Handler) {}

  createKeyboard() {
    return this.handler.cacheKeyboard(this.name, () => {
      return new InlineKeyboard().text(`× Cancel`, Name.wallet)
    })
  }

  async show() {
    const keyboard = this.createKeyboard()
    await this.handler.replyMsg(this.name, keyboard)
  }
}
