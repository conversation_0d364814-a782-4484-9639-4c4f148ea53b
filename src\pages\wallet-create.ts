import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { Name } from "../name"

export class WalletCreatePage {
  private name = Name.walletCreate
  constructor(private handler: Hand<PERSON>) {}

  createKeyboard() {
    return this.handler.cacheKeyboard(this.name, () => {
      const keyboard = new InlineKeyboard()
      const listName = BlockchainConfig.listChainName
      const listDisplayName = BlockchainConfig.listChainDisplayName
      for (let i = 0; i < listName.length; i++) {
        const itName = listName[i] as string
        const itDisplay = listDisplayName[i] as string
        keyboard.text(`🔗 ${itDisplay}`, `${Name.walletCreateName}:${itName}`)
        // Add row after every 2 buttons or if it's the last button
        if ((i + 1) % 2 === 0 || i === listName.length - 1) {
          keyboard.row()
        }
      }

      return keyboard.text(`... Back`, Name.wallet)
    })
  }

  async show() {
    await this.handler.sessionDelete()
    const keyboard = this.createKeyboard()
    await this.handler.updateMsg(this.name, keyboard)
  }
}
