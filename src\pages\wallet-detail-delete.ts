import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { WalletGuard } from "../guards/wallet"
import { BlockchainUtils } from "../blockchain/utils"
import { Name } from "../name"

export class WalletDetailDeletePage {
  private name = Name.walletDetailDelete
  private walletGuard: WalletGuard
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
  }

  createKeyboard(walletName: string) {
    return new InlineKeyboard().text(`... Back`, `${Name.walletDetail}:${walletName}`).text(`✅ Yes, Delete`, `${Name.walletDetailDeleteExecute}:${walletName}`).row()
  }

  async show() {
    const walletName = this.handler.callbackDataParams
    const wallet = await this.walletGuard.ensureExists(walletName)
    if (wallet === null) {
      return
    }

    const { chainDisplayName } = BlockchainConfig.get(wallet.chain as any)
    const keyboard = this.createKeyboard(walletName)
    await this.handler.updateMsg(this.name, keyboard, {
      walletName: wallet.name,
      walletAddress: wallet.address,
      chainName: chainDisplayName
    })
  }
}
