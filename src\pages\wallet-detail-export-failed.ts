import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { log } from "../utils/log"
import { Name } from "../name"

export class WalletDetailExportFailedPage {
  private name = Name.walletDetailExportFailed
  constructor(private handler: Handler) {}

  createKeyboard(walletName: string) {
    return new InlineKeyboard().text(`... Back`, `${Name.walletDetail}:${walletName}`)
  }

  async show(message: any, wallet: any) {
    log.error(`${this.name}: ${JSON.stringify(message)}`)
    const keyboard = this.createKeyboard(wallet.name)
    await this.handler.updateMsg(this.name, keyboard, {
      walletAddress: wallet.address
    })
  }
}
