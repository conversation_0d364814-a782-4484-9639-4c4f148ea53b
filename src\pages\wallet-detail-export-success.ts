import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { Name } from "../name"

export class WalletDetailExportSuccessPage {
  private name = Name.walletDetailExportSuccess
  constructor(private handler: Handler) {}

  createKeyboard(walletName: string) {
    return new InlineKeyboard().text(`... Back`, `${Name.walletDetail}:${walletName}`)
  }

  async show(wallet: any) {
    const { chainDisplayName } = BlockchainConfig.get(wallet.chain as any)
    const keyboard = this.createKeyboard(wallet.name)
    await this.handler.updateMsg(this.name, keyboard, {
      walletName: wallet.name,
      chainName: chainDisplayName,
      walletAddress: wallet.address,
      walletPrivateKey: wallet.privateKey
    })
  }
}
