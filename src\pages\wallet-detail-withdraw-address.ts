import { InlineKeyboard } from "grammy"
import type { Hand<PERSON> } from "../handler"
import { BlockchainWallet } from "../blockchain/wallet"
import { GeneralErrorPage } from "./general-error"
import { WalletDetailWithdrawAmountPage } from "./wallet-detail-withdraw-amount"
import { WalletDetailWithdrawWarnInvalidAddressPage } from "./wallet-detail-withdraw-warn-invalid-address"
import { WalletDetailWithdrawWarnInvalidDestinationPage } from "./wallet-detail-withdraw-warn-invalid-destination"
import { WalletGuard } from "../guards/wallet"
import { Name } from "../name"

export class WalletDetailWithdrawAddressPage {
  private name = Name.walletDetailWithdrawAddress
  private walletGuard: WalletGuard
  private generalErrorPage: GeneralErrorPage
  private walletDetailWithdrawAmountPage: WalletDetailWithdrawAmountPage
  private walletDetailWithdrawWarnInvalidAddressPage: WalletDetailWithdrawWarnInvalidAddressPage
  private walletDetailWithdrawWarnInvalidDestinationPage: WalletDetailWithdrawWarnInvalidDestinationPage
  constructor(private handler: Hand<PERSON>) {
    this.walletGuard = new WalletGuard(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.walletDetailWithdrawAmountPage = new WalletDetailWithdrawAmountPage(this.handler)
    this.walletDetailWithdrawWarnInvalidAddressPage = new WalletDetailWithdrawWarnInvalidAddressPage(this.handler)
    this.walletDetailWithdrawWarnInvalidDestinationPage = new WalletDetailWithdrawWarnInvalidDestinationPage(this.handler)
  }

  createKeyboard(walletName: string) {
    return new InlineKeyboard().text(`× Cancel`, `${Name.walletDetail}:${walletName}`)
  }

  async show() {
    const walletName = this.handler.callbackDataParams
    const wallet = await this.walletGuard.ensureExists(walletName)
    if (wallet === null) {
      return
    }

    // Set up session for address input
    await this.handler.sessionSet({
      method: `it-${this.name}`,
      params: { walletName, walletAddress: wallet.address, walletChain: wallet.chain }
    })

    const keyboard = this.createKeyboard(walletName)
    await this.handler.updateMsg(this.name, keyboard)
  }

  async input(sessionParams: Record<any, any>, input: string) {
    try {
      const { walletName, walletChain, walletAddress } = sessionParams

      // Validate address format
      const blockchainWallet = BlockchainWallet.get(walletChain)
      if (!blockchainWallet.isValidAddress(input)) {
        await this.walletDetailWithdrawWarnInvalidAddressPage.show(walletName)
        return
      }

      // Check if user is trying to send to themselves
      if (input.toLowerCase() === walletAddress.toLowerCase()) {
        await this.walletDetailWithdrawWarnInvalidDestinationPage.show(walletName)
        return
      }

      await this.walletDetailWithdrawAmountPage.show({ ...sessionParams, inputToAddress: input })
    } catch (error) {
      await this.generalErrorPage.show(`${this.name}:input`, error)
    }
  }
}
