import { InlineKeyboard } from "grammy"
import type { Handler } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { BlockchainWallet } from "../blockchain/wallet"
import { BlockchainUtils } from "../blockchain/utils"
import { GeneralErrorPage } from "./general-error"
import { WalletDetailWithdrawConfirmPage } from "./wallet-detail-withdraw-confirm"
import { WalletDetailWithdrawWarnMinAmountPage } from "./wallet-detail-withdraw-warn-min-amount"
import { WalletDetailWithdrawWarnInvalidAmountPage } from "./wallet-detail-withdraw-warn-invalid-amount"
import { WalletDetailWithdrawFailedEstimateFeePage } from "./wallet-detail-withdraw-failed-estimate-fee"
import { WalletDetailWithdrawFailedInsufficientBalancePage } from "./wallet-detail-withdraw-failed-insufficient-balance"
import { WalletGuard } from "../guards/wallet"
import { Name } from "../name"

export class WalletDetailWithdrawAmountPage {
  private name = Name.walletDetailWithdrawAmount
  private walletGuard: WalletGuard
  private generalErrorPage: GeneralErrorPage
  private walletDetailWithdrawConfirmPage: WalletDetailWithdrawConfirmPage
  private walletDetailWithdrawWarnMinAmountPage: WalletDetailWithdrawWarnMinAmountPage
  private walletDetailWithdrawWarnInvalidAmountPage: WalletDetailWithdrawWarnInvalidAmountPage
  private walletDetailWithdrawFailedEstimateFeePage: WalletDetailWithdrawFailedEstimateFeePage
  private walletDetailWithdrawFailedInsufficientBalancePage: WalletDetailWithdrawFailedInsufficientBalancePage
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.walletDetailWithdrawConfirmPage = new WalletDetailWithdrawConfirmPage(this.handler)
    this.walletDetailWithdrawWarnInvalidAmountPage = new WalletDetailWithdrawWarnInvalidAmountPage(this.handler)
    this.walletDetailWithdrawWarnMinAmountPage = new WalletDetailWithdrawWarnMinAmountPage(this.handler)
    this.walletDetailWithdrawFailedEstimateFeePage = new WalletDetailWithdrawFailedEstimateFeePage(this.handler)
    this.walletDetailWithdrawFailedInsufficientBalancePage = new WalletDetailWithdrawFailedInsufficientBalancePage(this.handler)
  }

  createKeyboard(walletName: string) {
    return new InlineKeyboard().text(`× Cancel`, `${Name.walletDetail}:${walletName}`).text(`🔄 Repeat`, `${Name.walletDetailWithdrawAddress}:${walletName}`)
  }

  async show(sessionParams: any) {
    const { walletName, walletChain } = sessionParams
    const { chainSymbol } = BlockchainConfig.get(walletChain)

    // Set up session for amount input
    await this.handler.sessionSet({
      method: `it-${this.name}`,
      params: sessionParams
    })

    const keyboard = this.createKeyboard(walletName)
    await this.handler.updateMsg(this.name, keyboard, {
      chainSymbol
    })
  }

  async input(sessionParams: any, input: string) {
    try {
      const { walletChain, walletName } = sessionParams
      const { chainSymbol, chainDecimals } = BlockchainConfig.get(walletChain)
      const amountInput = input.toLowerCase() === `max` ? `max` : isNaN(input as any) ? null : BlockchainUtils.parseUnits(input, chainDecimals)
      const wallet = await this.walletGuard.ensureExists(walletName)
      if (wallet === null) {
        return
      }

      // Parse amount input
      let amount = amountInput as bigint
      const currentBalance = wallet.balance
      if (amountInput === `max`) {
        // For max amount, we need to estimate fees first and subtract them
        const wallet = BlockchainWallet.get(walletChain)
        const feeEstimate = await wallet.estimateFee(`${currentBalance}`)
        if (feeEstimate.error) {
          await this.walletDetailWithdrawFailedEstimateFeePage.show(walletName, `could not estimate network fees: ${feeEstimate.error}`)
          return
        }

        const maxSendable = currentBalance - BigInt(feeEstimate.result as any) || 0n
        if (maxSendable <= 0n) {
          await this.walletDetailWithdrawFailedInsufficientBalancePage.show(walletName, `insufficient balance to cover network fees. You need at least ${feeEstimate.result} ${chainSymbol} for transaction fees.`)
          return
        }

        amount = maxSendable
      } else if (amountInput === null || amountInput <= 0n) {
        // Only numeric input and higher than 0
        await this.walletDetailWithdrawWarnInvalidAmountPage.show(walletName)
        return
      } else {
        // Check minimum amount (0.001 for most chains) !!! FOR DISPLAY ONLY !!!
        const minAmount = BlockchainUtils.parseUnits(`0.001`, chainDecimals)
        if (amountInput < minAmount) {
          await this.walletDetailWithdrawWarnMinAmountPage.show(walletName)
          return
        }

        // Check if amount exceeds balance
        if (amountInput > currentBalance) {
          const formatAmount = BlockchainUtils.formatUnits(amountInput, chainDecimals)
          const formatCurrentBalance = BlockchainUtils.formatUnits(currentBalance, chainDecimals)
          await this.walletDetailWithdrawFailedInsufficientBalancePage.show(wallet, `insufficient balance. You have ${formatCurrentBalance} (${chainSymbol}) but trying to send ${formatAmount} (${chainSymbol}).`)
          return
        }
      }

      // Show confirmation page
      await this.walletDetailWithdrawConfirmPage.show({ ...sessionParams, inputAmount: amount })
    } catch (error) {
      await this.generalErrorPage.show(`${this.name}:input`, error)
    }
  }
}
