import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { log } from "../utils/log"
import { Name } from "../name"

export class WalletDetailWithdrawFailedEstimateFeePage {
  private name = Name.walletDetailWithdrawFailedEstimateFee
  constructor(private handler: Handler) {}

  createKeyboard(walletName: string) {
    return new InlineKeyboard().text(`🔄 Try Again`, `${Name.walletDetailWithdrawAddress}:${walletName}`).text(`💼 My Wallets`, Name.wallet).row().text(`... Back`, `${Name.walletDetail}:${walletName}`)
  }

  async show(walletName: string, message: any) {
    await this.handler.sessionDelete()

    log.failed(`${this.name}: ${JSON.stringify(message)}`)
    const keyboard = this.createKeyboard(walletName)
    await this.handler.updateMsg(this.name, keyboard)
  }
}
