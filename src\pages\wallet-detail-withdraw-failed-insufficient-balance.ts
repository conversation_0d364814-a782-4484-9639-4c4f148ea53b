import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { log } from "../utils/log"
import { Name } from "../name"

export class WalletDetailWithdrawFailedInsufficientBalancePage {
  private name = Name.walletDetailWithdrawFailedInsufficientBalance
  constructor(private handler: <PERSON><PERSON>) {}

  createKeyboard(walletName: string) {
    return new InlineKeyboard().text(`🔄 Try Again`, `${Name.walletDetailWithdrawAddress}:${walletName}`).text(`💼 My Wallets`, Name.wallet).row().text(`... Back`, `${Name.walletDetail}:${walletName}`)
  }

  async show(walletName: string, message: any) {
    log.failed(`${this.name}: ${JSON.stringify(message)}`)
    await this.handler.sessionDelete()
    const keyboard = this.createKeyboard(walletName)
    await this.handler.updateMsg(this.name, keyboard)
  }
}
