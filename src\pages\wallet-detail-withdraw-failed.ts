import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { log } from "../utils/log"
import { Name } from "../name"

export class WalletDetailWithdrawFailedPage {
  private name = Name.walletDetailWithdrawFailed
  constructor(private handler: Handler) {}

  createKeyboard(walletName: string) {
    return new InlineKeyboard().text(`🔄 Try Again`, `${Name.walletDetailWithdrawAddress}:${walletName}`).text(`💼 My Wallets`, Name.wallet).row().text(`... Back`, `${Name.walletDetail}:${walletName}`)
  }

  async show(sessionParams: any, message: any) {
    const { walletName, walletChain, inputToAddress, inputAmount } = sessionParams
    const { chainDisplayName, chainSymbol } = BlockchainConfig.get(walletChain)

    log.failed(`${this.name}: ${JSON.stringify(message)}`)
    await this.handler.sessionDelete()

    // // Record failed transaction in history
    // await ModelTrades.create(
    //   walletChain,
    //   false, // success = false
    //   inputAmount,
    //   `S` // S for send/withdraw
    // )

    const keyboard = this.createKeyboard(walletName)
    await this.handler.updateMsg(this.name, keyboard, {
      walletName,
      chainName: chainDisplayName,
      toAddress: inputToAddress,
      amount: inputAmount,
      chainSymbol,
      failedMessage: message
    })
  }
}
