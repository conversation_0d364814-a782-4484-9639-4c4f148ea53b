import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { BlockchainUtils } from "../blockchain/utils"
import { Name } from "../name"

export class WalletDetailWithdrawSuccessPage {
  private name = Name.walletDetailWithdrawSuccess
  constructor(private handler: Handler) {}

  createKeyboard(walletName: string) {
    return new InlineKeyboard().text(`💼 My Wallets`, Name.wallet).row().text(`... Back`, `${Name.walletDetail}:${walletName}`).text(`≡ Home`, Name.start)
  }

  async show(sessionParams: any, legacyData: { txhash: string; actualFee: any; newBalance: bigint }) {
    const { walletName, walletChain, inputToAddress, inputAmount } = sessionParams
    const { chainDisplayName, chainSymbol, chainDecimals, explorerUrl } = BlockchainConfig.get(walletChain)
    const { txhash, actualFee, newBalance } = legacyData
    await this.handler.sessionDelete()

    // // Record successful transaction in history
    // await ModelTrades.create(
    //   wallet.id,
    //   walletChain,
    //   true, // success = true
    //   inputAmount,
    //   `S` // S for send/withdraw
    // )

    const keyboard = this.createKeyboard(walletName)
    await this.handler.updateMsg(this.name, keyboard, {
      walletName,
      chainName: chainDisplayName,
      toAddress: inputToAddress,
      amount: inputAmount,
      chainSymbol,
      actualFee,
      txHash: txhash,
      explorerUrl,
      newBalance: BlockchainUtils.formatUnits(newBalance, chainDecimals)
    })
  }
}
