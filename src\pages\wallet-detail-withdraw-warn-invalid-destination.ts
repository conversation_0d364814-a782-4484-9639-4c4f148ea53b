import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class WalletDetailWithdrawWarnInvalidDestinationPage {
  private name = Name.walletDetailWithdrawWarnInvalidDestination
  constructor(private handler: Handler) {}

  createKeyboard(walletName: string) {
    return new InlineKeyboard().text(`× Cancel`, `${Name.walletDetail}:${walletName}`).text(`🔄 Repeat`, `${Name.walletDetailWithdrawAddress}:${walletName}`)
  }

  async show(walletName: string) {
    const keyboard = this.createKeyboard(walletName)
    await this.handler.updateMsg(this.name, keyboard)
  }
}
