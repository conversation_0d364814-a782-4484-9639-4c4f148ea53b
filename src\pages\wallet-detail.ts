import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Time } from "../utils/time"
import { BlockchainConfig } from "../blockchain/config"
import { BlockchainUtils } from "../blockchain/utils"
import { WalletGuard } from "../guards/wallet"
import { Name } from "../name"

export class WalletDetailPage {
  private name = Name.walletDetail
  private walletGuard: WalletGuard
  constructor(private handler: Hand<PERSON>) {
    this.walletGuard = new WalletGuard(this.handler)
  }

  createKeyboard(walletName: string) {
    return new InlineKeyboard()
      .text(`🔄 Refresh`, `${this.name}:${walletName}`)
      .text(`💸 Withdraw`, `${Name.walletDetailWithdrawAddress}:${walletName}`)
      .row()
      .text(`🔑 Export`, `${Name.walletDetailExport}:${walletName}`)
      .text(`🗑️ Delete`, `${Name.walletDetailDelete}:${walletName}`)
      .row()
      .text(`... Back`, Name.wallet)
      .text(`≡ Home`, Name.start)
  }

  async show() {
    await this.handler.sessionDelete() // Delete session if have
    const walletName = this.handler.callbackDataParams
    const wallet = await this.walletGuard.ensureExists(walletName)
    if (wallet === null) {
      return
    }

    const { chainId, chainDisplayName, chainSymbol, chainDecimals } = BlockchainConfig.get(wallet.chain as any)
    const keyboard = this.createKeyboard(walletName)
    await this.handler.updateMsg(this.name, keyboard, {
      walletName: wallet.name,
      walletAddress: wallet.address,
      walletBalance: BlockchainUtils.formatUnits(wallet.balance, chainDecimals),
      walletCreatedDate: Time.format(wallet.createdAt),
      walletCreatedBy: wallet.createdBy,
      chainName: chainDisplayName,
      chainSymbol,
      chainId,
      timestamp: Time.nowUnix().toString()
    })
  }
}
