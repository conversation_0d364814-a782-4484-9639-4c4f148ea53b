import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class WalletEmptyPage {
  private name = Name.walletEmpty
  constructor(private handler: Handler) {}

  createKeyboard() {
    return this.handler.cacheKeyboard(this.name, () => {
      return new InlineKeyboard().text(`➕ Create Wallet`, Name.walletCreate).text(`📥 Import Wallet`, Name.walletImport).row().text(`... Back`, Name.start)
    })
  }

  async show() {
    const keyboard = this.createKeyboard()
    await this.handler.updateMsg(this.name, keyboard)
  }
}
