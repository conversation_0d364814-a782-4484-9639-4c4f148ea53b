import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class WalletImportFailedPage {
  private name = Name.walletImportFailed
  constructor(private handler: Hand<PERSON>) {}

  createKeyboard() {
    return this.handler.cacheKeyboard(this.name, () => {
      return new InlineKeyboard().text(`🔄 Try Again`, Name.walletImport).text(`≡ Home`, Name.start)
    })
  }

  async show() {
    const keyboard = this.createKeyboard()
    await this.handler.replyMsg(this.name, keyboard)
  }
}
