import { InlineKeyboard } from "grammy"
import type { Hand<PERSON> } from "../handler"
import { WalletModel } from "../db/models/wallet"
import { BlockchainConfig } from "../blockchain/config"
import { WalletImportPrivateKeyPage } from "./wallet-import-private-key"
import { WalletImportWarnNameExistsPage } from "./wallet-import-warn-name-exists"
import { WalletImportWarnNameInvalidPage } from "./wallet-import-warn-name-invalid"
import { GeneralErrorPage } from "./general-error"
import { Name } from "../name"

export class WalletImportNamePage {
  private name = Name.walletImportName
  private generalErrorPage: GeneralErrorPage
  private walletImportPrivateKeyPage: WalletImportPrivateKeyPage
  private walletImportWarnNameExistsPage: WalletImportWarnNameExistsPage
  private walletImportWarnNameInvalidPage: WalletImportWarnNameInvalidPage
  constructor(private handler: Handler) {
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.walletImportPrivateKeyPage = new WalletImportPrivateKeyPage(this.handler)
    this.walletImportWarnNameExistsPage = new WalletImportWarnNameExistsPage(this.handler)
    this.walletImportWarnNameInvalidPage = new WalletImportWarnNameInvalidPage(this.handler)
  }

  createKeyboard() {
    return this.handler.cacheKeyboard(this.name, () => {
      return new InlineKeyboard().text(`× Cancel`, Name.wallet)
    })
  }

  async show() {
    const chainName = this.handler.callbackDataParams
    const { chainSymbol, chainDisplayName } = BlockchainConfig.get(chainName as any)
    await this.handler.sessionSet({
      method: `it-${this.name}`,
      params: { chainName }
    })

    const keyboard = this.createKeyboard()
    await this.handler.updateMsg(this.name, keyboard, {
      chainName: chainDisplayName,
      chainSymbol
    })
  }

  async input(sessionParams: Record<any, any>, input: string) {
    try {
      // Validate wallet name
      if (input.length > 10 || !/^[a-zA-Z0-9_/-]+$/.test(input)) {
        await this.walletImportWarnNameInvalidPage.show()
        return
      }

      // Check if wallet name already exists
      const existingWallet = await WalletModel.getByNameForUser(this.handler.userId, input)
      if (existingWallet) {
        await this.walletImportWarnNameExistsPage.show()
        return
      }

      await this.walletImportPrivateKeyPage.show({ ...sessionParams, inputName: input })
    } catch (err) {
      await this.generalErrorPage.show(this.name, err)
    }
  }
}
