import { InlineKeyboard } from "grammy"
import type { Hand<PERSON> } from "../handler"
import { WalletModel } from "../db/models/wallet"
import { BlockchainConfig } from "../blockchain/config"
import { BlockchainWallet } from "../blockchain/wallet"
import { GeneralErrorPage } from "./general-error"
import { WalletImportFailedPage } from "./wallet-import-failed"
import { WalletImportSuccessPage } from "./wallet-import-success"
import { WalletImportWarnNameExistsPage } from "./wallet-import-warn-name-exists"
import { WalletImportWarnDuplicateAddressPage } from "./wallet-import-warn-duplicate-address"
import { WalletImportWarnPrivateKeyInvalidPage } from "./wallet-import-warn-private-key-invalid"
import { Name } from "../name"

export class WalletImportPrivateKeyPage {
  private name = Name.walletImportPrivateKey
  private generalErrorPage: GeneralErrorPage
  private walletImportFailedPage: WalletImportFailedPage
  private walletImportSuccessPage: WalletImportSuccessPage
  private walletImportWarnNameExistsPage: WalletImportWarnNameExistsPage
  private walletImportWarnDuplicateAddressPage: WalletImportWarnDuplicateAddressPage
  private walletImportWarnPrivateKeyInvalidPage: WalletImportWarnPrivateKeyInvalidPage
  constructor(private handler: Handler) {
    this.generalErrorPage = new GeneralErrorPage(this.handler)
    this.walletImportFailedPage = new WalletImportFailedPage(this.handler)
    this.walletImportSuccessPage = new WalletImportSuccessPage(this.handler)
    this.walletImportWarnNameExistsPage = new WalletImportWarnNameExistsPage(this.handler)
    this.walletImportWarnDuplicateAddressPage = new WalletImportWarnDuplicateAddressPage(this.handler)
    this.walletImportWarnPrivateKeyInvalidPage = new WalletImportWarnPrivateKeyInvalidPage(this.handler)
  }

  createKeyboard() {
    return this.handler.cacheKeyboard(WalletImportPrivateKeyPage.name, () => {
      return new InlineKeyboard().text(`× Cancel`, Name.walletImport)
    })
  }

  async show(sessionParams: any) {
    const { chainName, inputName } = sessionParams
    const { chainSymbol } = BlockchainConfig.get(chainName)
    await this.handler.sessionSet({
      method: `it-${this.name}`,
      params: sessionParams
    })

    const keyboard = this.createKeyboard()
    await this.handler.replyMsg(this.name, keyboard, {
      chainName,
      chainSymbol,
      walletName: inputName
    })
  }

  async input(sessionParams: any, input: string) {
    const { chainName, inputName } = sessionParams
    const { chainId, chainDisplayName } = BlockchainConfig.get(chainName)
    async function getAddress() {
      try {
        return await BlockchainWallet.get(chainName).getAddress(input)
      } catch (err) {
        return null
      }
    }

    try {
      // Derive address from private key
      const gWalletAddress = await getAddress()
      if (!gWalletAddress) {
        await this.walletImportWarnPrivateKeyInvalidPage.show()
        return
      }

      // Get chain ID from blockchain config
      const wallet = await WalletModel.import(this.handler.userId, inputName, chainName, chainId, gWalletAddress, input, this.handler.passwordWallet)

      if (wallet && typeof wallet === `object`) this.walletImportSuccessPage.show(chainDisplayName, wallet)
      else if (wallet === `DUPLICATE_NAME`) this.walletImportWarnNameExistsPage.show()
      else if (wallet === `DUPLICATE_ADDRESS`) this.walletImportWarnDuplicateAddressPage.show(chainDisplayName, gWalletAddress)
      else this.walletImportFailedPage.show()
    } catch (error) {
      await this.generalErrorPage.show(this.name, error)
    }
  }
}
