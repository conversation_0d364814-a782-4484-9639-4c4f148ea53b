import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class WalletImportSuccessPage {
  private name = Name.walletImportSuccess
  constructor(private handler: Hand<PERSON>) {}

  createKeyboard(walletName: string) {
    return (
      new InlineKeyboard()
        //
        .text(`👁️ View Wallet`, `${Name.walletDetail}:${walletName}`)
        .text(`📥 Import Another`, Name.walletImport)
        .row()
        .text(`... Back`, Name.wallet)
        .text(`≡ Home`, Name.start)
    )
  }

  async show(chainDisplayName: string, wallet: any) {
    await this.handler.sessionDelete()
    const successKeyboard = this.createKeyboard(wallet.name)
    await this.handler.replyMsg(this.name, successKeyboard, {
      chainName: chainDisplayName,
      walletName: wallet.name,
      walletAddress: wallet.address
    })
  }
}
