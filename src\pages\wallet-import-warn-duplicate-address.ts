import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class WalletImportWarnDuplicateAddressPage {
  private name = Name.walletImportWarnDuplicateAddress
  constructor(private handler: Handler) {}

  createKeyboard() {
    return this.handler.cacheKeyboard(this.name, () => {
      return new InlineKeyboard().text(`× Cancel`, Name.walletImport)
    })
  }

  async show(chainDisplayName: string, walletAddress: string) {
    const keyboard = this.createKeyboard()
    await this.handler.replyMsg(this.name, keyboard, {
      chainName: chainDisplayName,
      walletAddress
    })
  }
}
