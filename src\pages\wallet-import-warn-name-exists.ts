import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { Name } from "../name"

export class WalletImportWarnNameExistsPage {
  private name = Name.walletImportWarnNameExists
  constructor(private handler: Handler) {}

  createKeyboard() {
    return this.handler.cacheKeyboard(this.name, () => {
      return new InlineKeyboard().text(`× Cancel`, Name.walletImport)
    })
  }

  async show() {
    const keyboard = this.createKeyboard()
    await this.handler.replyMsg(this.name, keyboard)
  }
}
