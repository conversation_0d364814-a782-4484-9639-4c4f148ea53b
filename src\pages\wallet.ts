import { InlineKeyboard } from "grammy"
import type { <PERSON><PERSON> } from "../handler"
import { BlockchainConfig } from "../blockchain/config"
import { GeneralErrorPage } from "./general-error"
import { WalletGuard } from "../guards/wallet"
import { BlockchainUtils } from "../blockchain/utils"
import { Name } from "../name"

export class WalletPage {
  private name = Name.wallet
  private walletGuard: WalletGuard
  private generalErrorPage: GeneralErrorPage
  constructor(private handler: Handler) {
    this.walletGuard = new WalletGuard(this.handler)
    this.generalErrorPage = new GeneralErrorPage(this.handler)
  }

  /**
   * Create wallet list keyboard with pagination
   * @param wallets Array of wallet objects
   * @param currentPage Current page number (0-based)
   * @param totalPages Total number of pages
   */
  createKeyboard(wallets: any[], currentPage: number, totalPages: number) {
    const keyboard = new InlineKeyboard()
    const prefixNext = Name.walletDetail
    const prefixPagination = this.name

    // Add wallet buttons
    for (let index = 0; index < wallets.length; index++) {
      const { name, balance, chainSymbol } = wallets[index]
      const displayName = `${name} (${balance}) ${chainSymbol}`

      keyboard.text(displayName, `${prefixNext}:${name}`).row()
    }

    // Add pagination if needed
    if (totalPages > 1) {
      keyboard.row()
      if (currentPage > 0) {
        keyboard.text(`⬅️ Previous`, `${prefixPagination}:${currentPage - 1}`)
      }
      keyboard.text(`${currentPage + 1}/${totalPages}`, `noop`)
      if (currentPage < totalPages - 1) {
        keyboard.text(`➡️ Next`, `${prefixPagination}:${currentPage + 1}`)
      }
    }

    // Add action buttons
    keyboard.row().text(`➕ Create`, Name.walletCreate).text(`📥 Import`, Name.walletImport).row().text(`... Back`, Name.start)
    return keyboard
  }

  async show() {
    try {
      await this.handler.sessionDelete()
      const part = this.handler.callbackData.split(`:`)[1] as string // dont use callbackDataParams because this spesific
      const page = parseInt(part === undefined ? `0` : part)
      const wallets = await this.walletGuard.ensureExistAll()
      if (wallets === null) {
        return
      }

      // Add wallet buttons (max 5 per page for better UX)
      const walletsPerPage = 5
      const totalPages = Math.ceil(wallets.length / walletsPerPage)
      const currentPage = Math.max(0, Math.min(page, totalPages - 1))
      const startIndex = currentPage * walletsPerPage
      const endIndex = Math.min(startIndex + walletsPerPage, wallets.length)

      // Get wallets for current page with balance info
      const pageWallets = wallets.slice(startIndex, endIndex).map((it: any) => {
        const { chainSymbol, chainDecimals } = BlockchainConfig.get(it.chain)
        const balance = BlockchainUtils.formatUnits(it.balance, chainDecimals)
        return {
          ...it,
          balance,
          chainSymbol,
          displayKeyboard: `${balance} (${chainSymbol})`
        }
      })
      const keyboard = this.createKeyboard(pageWallets, currentPage, totalPages)
      await this.handler.updateMsg(this.name, keyboard, {
        totalWallets: wallets.length,
        currentPage: currentPage + 1,
        totalPages,
        startIndex,
        walletList: pageWallets
      })
    } catch (error) {
      await this.generalErrorPage.show(this.name, error)
    }
  }
}
