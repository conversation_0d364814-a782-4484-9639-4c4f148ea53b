export class TemplateEngine {
  static compile(html: string) {
    const renderRegex = /<render[^>]*>([\s\S]*?)<\/render>/g
    const renderMatches = [...html.matchAll(renderRegex)]
    for (const match of renderMatches) {
      const codeStr = match[1]?.trim() || ``
      html = html.replace(match[0], `\${(() => { if (!data) return ''; try { with(data) { ` + codeStr + ` } } catch(e) { return '' } })()}`)
    }
    html = html.replace(/\{\{(\w+)\}\}/g, (_, key) => `\${(data && data["${key}"]) ?? ""}`)
    const functionBody = `return ` + `\`` + html + `\``
    return new Function(`data`, functionBody) as (data?: Record<string, any>) => string
  }
}
