import dayjs from "dayjs"
import utc from "dayjs/plugin/utc"
import tz from "dayjs/plugin/timezone"

dayjs.extend(utc)
dayjs.extend(tz)

export class Time {
  static zone = `Asia/Jakarta`
  static template = `YYYY-MM-DD HH:mm:ss`
  static format(time: any, format = this.template) {
    return dayjs(time).format(format)
  }

  static now(format = this.template) {
    return dayjs.utc().tz(this.zone).format(format)
  }

  static nowUnix() {
    return dayjs.utc().tz(this.zone).valueOf()
  }
}
