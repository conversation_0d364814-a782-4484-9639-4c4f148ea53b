import { Content } from "./src/utils/content"

const content = new Content()
const data = {
  totalWallets: 100,
  currentPage: 1,
  totalPages: 3,
  startIndex: 0,
  walletList: [
    {
      name: `W1`,
      chain: `Ethereum`,
      balance: 0n,
      symbol: `ETH`,
      address: `0x1234567890`
    },
    {
      name: `W1`,
      chain: `Ethereum`,
      balance: 0n,
      symbol: `ETH`,
      address: `0x1234567890`
    },
    {
      name: `W1`,
      chain: `Ethereum`,
      balance: 0n,
      symbol: `ETH`,
      address: `0x1234567890`
    },
    {
      name: `W1`,
      chain: `Ethereum`,
      balance: 0n,
      symbol: `ETH`,
      address: `0x1234567890`
    },
    {
      name: `W1`,
      chain: `Ethereum`,
      balance: 0n,
      symbol: `ETH`,
      address: `0x1234567890`
    }
  ]
}
const res = content.get(`wallet`, data)
console.log(res)

console.log(`\n=== PERFORMANCE TEST ===`)
const start = performance.now()
for (let i = 0; i < 1000000; i++) {
  //   render(completeData)
  content.get(`wallet`, data)
}
const end = performance.now()
const time = end - start
const opsPerSec = Math.round(1000000 / (time / 1000))
console.log(`⚡ Time: ${time.toFixed(2)}ms for 1M ops`)
console.log(`🚀 Performance: ${opsPerSec.toLocaleString()} ops/sec`)
