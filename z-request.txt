implementasikan ke diagram mermaid ke file ./diagram.txt dari flow di bawah dan dari code schema di bawah : 

1. Ma<PERSON>kkan alamat token
Langkah pertama adalah memasukkan alamat token yang ingin digunakan.
2. Tentukan jumlah pembelian awal
Setelah itu, tentukan jumlah dana yang akan digunakan untuk pembelian awal.
Contoh: 1 SOL.
3. Atur parameter rebuy dan sell
Rebuy: 10%
Sell: 20%
4. Tentukan batas maksimal transaksi jual yang tertunda
Masukkan jumlah maksimum pending sell transactions, misalnya: 15.
Tujuan batas ini adalah agar bot pause jika terlalu banyak token belum terjual. Ini untuk mencegah terus berinvestasi di token yang harganya terus menurun.
5. Tekan OK untuk memulai proses
Setelah semua konfigurasi selesai, tekan OK untuk memulai.
6. Bot melakukan pembelian pertama
Bot membeli token senilai 1 SOL.
Contoh hasil: 100 token.
7. Buat order jual pertama
Bot akan membuat order jual untuk seluruh token tersebut saat harga naik 20%.
8. Buat dua order rebuy
Bot akan langsung membuat 2 order rebuy:
1 order di harga 10% lebih tinggi dari pembelian terakhir
1 order di harga 10% lebih rendah dari pembelian terakhir
9. Salah satu order rebuy tereksekusi
Jika salah satu order rebuy tereksekusi:
Order yang lain dibatalkan
Bot membuat order jual untuk token tersebut dengan target naik 20%
10. Order rebuy lainnya tetap aktif
Selain itu, bot akan membuat ulang dua order rebuy baru seperti sebelumnya (±10%).
11. Proses terus berulang
Siklus akan terus berulang selama batas maksimum pending sell (langkah 4) belum tercapai.

// code schema
export const configSchema = pgTable(`config`, {
  id: varchar({ length: 25 }).notNull().primaryKey(), // config id create with cuid
  walletId: varchar({ length: 25 }) // wallet id as owner this config
    .notNull()
    .references(() => walletSchema.id, { onDelete: `cascade` }),
  name: varchar({ length: 10 }).notNull(), // config name
  initAmountIn: bigint({ mode: `bigint` }).notNull(), // jumlah awal token yang dikirim
  tokenIn: varchar({ length: 50 }).notNull(), // token yang dikirim
  tokenOut: varchar({ length: 50 }).notNull(), // token yang diterima
  rebuyBps: bigint({ mode: `bigint` }).notNull(), // % rebuy  (basis-point)
  sellBps: bigint({ mode: `bigint` }).notNull(), // % profit (basis-point)
  maxPendingSell: bigint({ mode: `bigint` }).notNull(), // max count pending sell
  status: text({ enum: [`inactive`, `active`] })
    .notNull()
    .default(`inactive`),
  createdAt: timestamp().defaultNow(),
  updatedAt: timestamp().defaultNow()
})

export const monitorSchema = pgTable(`monitor`, {
  id: bigserial({ mode: `bigint` }).notNull().primaryKey(), // just increment id
  configId: varchar({ length: 25 }) // wallet id as owner this config
    .notNull()
    .references(() => configSchema.id, { onDelete: `cascade` }),
  maxPendingSell: bigint({ mode: `bigint` }) // max count pending sell
    .notNull()
    .references(() => configSchema.maxPendingSell, { onDelete: `cascade` }),
  status: text({ enum: [`open`, `close`] }) // status monitor
    .notNull()
    .default(`open`),
  pendingSell: bigint({ mode: `bigint` }).notNull().default(0n), // current count pending sell
  totalAmountIn: bigint({ mode: `bigint` }).notNull(), // jumlah token yang dikirim
  totalAmountOut: bigint({ mode: `bigint` }).notNull(), // jumlah token yang diterima.
  updatedAt: timestamp().defaultNow(),
  createdAt: timestamp().defaultNow()
})

export const txSchema = pgTable(`tx`, {
  id: bigserial({ mode: `bigint` }).notNull().primaryKey(), // just increment id
  configId: varchar({ length: 25 }).notNull(), // config Id from schemaConfig.id
  chain: varchar({ length: 10 }).notNull(), // example: ethereum, solana
  hash: varchar({ length: 128 }).notNull().unique(), // hash transaction langsung dari blockchain atau local preprocess statuspending
  from: varchar({ length: 50 }).notNull(), // sender transaction (wallet)
  to: varchar({ length: 50 }).notNull(), // receiver transaction (smartcontract)
  type: text({ enum: [`buy`, `sell`, `buy_up`, `buy_down`] }).notNull(), // alasan pembuatan tx. buy: membeli harga market | sell: menjual harga market | buy_up: membeli karena harga naik (acuan) | buy_down: membeli karena harga turun (acuan)
  amountIn: bigint({ mode: `bigint` }).notNull(), // jumlah token yang dikirim
  amountOut: bigint({ mode: `bigint` }).notNull(), // jumlah token yang diterima.
  slippage: bigint({ mode: `bigint` }).notNull(), // slippage yang di set dengan BASIS
  tokenIn: varchar({ length: 50 }).notNull(), // token yang dikirim
  tokenOut: varchar({ length: 50 }).notNull(), // token yang diterima
  status: text({ enum: [`pending`, `success`, `failed`] }) // status transaction langsung dari blockchain
    .notNull()
    .default(`pending`),
  updateAt: timestamp().defaultNow(),
  createdAt: timestamp().defaultNow()
}) 