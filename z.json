[{"id": "code_quality", "path": "./src/pages", "rule": "Produce senior‐engineer–level, production-quality code in English.", "detail": {"format": "Clean structure without inline comments inside class bodies.", "comment_placement": "Only allow a single comment block immediately above the class declaration.", "robustness": "Code must be robust, maintainable, and production-grade."}}, {"id": "string_style", "path": "*", "rule": "Use ES6 template literals (backticks) instead of single or double quotes.", "detail": {"applies_to": "all source files where string construction occurs"}}, {"id": "page_method_constraints", "path": "./src/pages/*.ts", "rule": "Only `show` and `create_keyboard` methods are allowed in page classes.", "detail": {"show": "Acts as the controller or entry point.", "createKeyboard": "Create keyboard", "restriction": "Do not introduce other methods without prior approval."}}, {"id": "page_invocation", "path": "./src/pages/*.ts", "rule": "Navigate between pages only by calling allowed methods like `show`.", "detail": {"disallowed": "Never use `this.handler.*` directly.", "method": "Only use `show()` for navigation."}}, {"id": "instance_initialization", "path": "./src/pages/*.ts", "rule": "All target pages must be instantiated in the constructor before being used.", "detail": {"naming_convention": "Use camel_case matching the class name.", "example": "`user_banned = new UserBanned()`"}}, {"id": "html_pairing", "path": ["./content/*.html", "./src/pages/*.ts"], "rule": "Each `.html` file must have a matching `.ts` file with the same base name.", "detail": {"usage": "Page file must call its HTML counterpart via `show()`."}}, {"id": "status_pages_priority", "path": ["./content/*.html", "./src/pages/*.ts"], "rule": "Prioritize creating pages for `failed` and `success` states.", "detail": {"fallback": "Use `general-*.ts` only for generic pages."}}, {"id": "html_parsing_requirement", "path": "./content/*.html", "rule": "HTML content must be relevant for Telegram bot and parsable by the system.", "detail": {"parser": "Must be compatible with `./src/utils/template_engine.ts` and `./src/utils/content.ts`."}}, {"id": "guards_behavior", "path": "./src/guards", "rule": "Guards fetch specific data and trigger fallbacks via `.show()` when conditions are met.", "detail": {"fallback_behavior": "Do not directly invoke handlers; always redirect via `.show()`."}}]