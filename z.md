Berikut adalah versi `.md` (Markdown) dari instruksi dan aturan yang kamu buat, **diformat agar mudah dibaca dan dipahami oleh AI coder dan <PERSON><PERSON> (senior engineer/maintainer)**. Markdown ini cocok dijadikan sebagai _project rulebook_, _README guidelines_, atau bahkan _system prompt_ jika dibaca oleh AI tools.

---

## 📘 Project Instructions: Telegram Trading Bot (Using Grammy.js + Bun)

### 🧠 Context

You are building a **production-grade Telegram trading bot** using the [Grammy](https://grammy.dev) framework. The bot is coded in **TypeScript**, runs on **Bun**, and must follow strict engineering and architectural guidelines to ensure quality, consistency, and maintainability.

---

### 🏗️ Environment

| Property    | Value        |
| ----------- | ------------ |
| Mode        | `production` |
| Language    | `TypeScript` |
| Runtime     | `Bun`        |
| Bot Library | `Grammy`     |

---

## 📜 Coding Regulations

### ✅ 1. Code Quality and Comments

- Write **senior-engineer–level, production-quality** code in English.
- Format all code cleanly.
- **Comments are only allowed**:

  - As a single block **immediately above class declarations**.
  - No inline or inside-method comments inside class bodies.

#### Context:

- `code_quality`: production-grade, robust, and maintainable
- `comment_placement`: comments only above class declaration

---

### ✅ 2. String Formatting

- Use **ES6 template literals** (`` ` `` backticks) instead of `'single'` or `"double"` quotes whenever possible.

---

### ✅ 3. HTML Content Parsing

- Ensure every file in `./content/*.html`:
  - Contains **Telegram-bot–relevant** content.
  - Is **parsable** by:
    `./src/utils/template-engine.ts` (via `./src/utils/content.ts`).

---

### ✅ 4. Page Method Restrictions

- In `./src/pages/*.ts`, **only these methods are allowed** on page classes:
  - `show`
  - `createKeyboard`
- Do **not add new methods** without explicit approval.

#### Context:

- `show`: entry point (acts as controller)

---

### ✅ 5. Page & Content File Pairing

- Every file in `./content/` **must have a matching file** in `./src/pages/` with the **same base name**.
  - Example:
    `user-banned.html` ⟷ `user-banned.ts`
- To invoke another page, use **only allowed methods** like `show`.

#### Context:

- `page_invocation`: use allowed methods for navigation

---

### ✅ 6. Status-Driven Page Generation

- Prioritize creating page-content pairs for:

  - `failed`
  - `success`

- If only generic content is needed, use files prefixed with:
  `./src/pages/general-*.ts`

---

### ✅ 7. Navigation Restrictions

- **Do not navigate directly** via `this.handler.*`
- Always use approved methods (`show`, `createKeyboard`) for page transitions.

---

### ✅ 8. Page Instance Initialization

- **Do not display another page** without first:

  - Initializing its instance in the constructor
  - Naming the instance in **camelCase** matching the class name

#### Context:

- `initialization`: instantiate all pages in constructor
- `naming_convention`: use camelCase matching the class name for property names

---

## 📁 File Structure Expectations

```
project-root/
├── content/
│   ├── failed.html
│   ├── success.html
│   └── user-banned.html
├── src/
│   └── pages/
│       ├── failed.ts
│       ├── success.ts
│       └── user-banned.ts
```

---

## 📌 Summary of Method Constraints

| Method             | Description                         | Allowed? |
| ------------------ | ----------------------------------- | -------- |
| `show()`           | Main controller entry point         | ✅       |
| `createKeyboard()` | Builds inline keyboard for the page | ✅       |
| Any other          | Must request approval before adding | ❌       |

---

### ⚠️ Important

- **Do not bypass page navigation rules.**
- **Do not write methods unless defined above.**
- **All content must align with Telegram bot flow.**

---

## ✅ You're now ready to build with confidence.
