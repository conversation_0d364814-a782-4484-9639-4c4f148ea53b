// import { eq } from "drizzle-orm"
// import { db } from "./src/db/index"
// import { tableWallets } from "./src/db/schema/wallets"

// // Get all tables and their data
// const tables = await db.execute(
//   `SELECT table_name
//    FROM information_schema.tables
//    WHERE table_schema = 'public'`
// )

// for (const table of (tables as any).rows) {
//   const tableName = table.table_name
//   console.log(`\n=== Data from ${tableName} ===`)
//   const { rows }: any = await db.execute(`SELECT * FROM ${tableName}`)
//   console.log(rows)
// }

// import { toBeHex, hexlify, toUtf8Bytes } from "ethers"

// const res_1 = hexlify(toUtf8Bytes(`5123456789-1`))
// const res_2 = toBeHex(`ss`)
// console.log(res_1, res_2)
//

// import { Eta } from "eta"
// import Benchmark from "benchmark"
// const eta = new Eta({ useWith: true, tags: [`{{`, `}}`] })
// const template = "Hello, {{=name}}!"
// const compiled = eta.compile(template)
// const data = { name: "Damar" }
// const suite = new Benchmark.Suite(undefined)

// suite
//   .add("Eta.js (precompiled)", () => {
//     compiled.call(eta, data)
//   })
//   .on("cycle", (event: any) => {
//     console.log(String(event.target))
//   })
//   .on("complete", function () {
//     console.log("Fastest is " + this.filter("fastest").map("name"))
//   })
//   .run({ async: false })

// import micromustache from "micromustache"
// import Benchmark from "benchmark"
// const template = `Hello, {{name}}!`
// const compile = micromustache.compile(template)
// const data = {
//   name: `Wkwkwkwkwkwk`
// }
// const suite = new Benchmark.Suite(undefined)
// suite
//   .add("Micromustache.js (precompiled)", () => {
//     micromustache.render(template, data)
//     // compile.render(data)
//   })
//   .on("cycle", (event: any) => {
//     console.log(String(event.target))
//   })
//   .on("complete", function () {
//     console.log("Fastest is " + this.filter("fastest").map("name"))
//   })
//   .run({ async: false })

// import dot from "dot"
// import Benchmark from "benchmark"
// const template = `Hello, {{self.name}}`
// const compile = dot.template(template, {
//   evaluate: /\{\{code([\s\S]+?)\}\}/g, // {{code JavaScript code }}
//   interpolate: /\{\{([\s\S]+?)\}\}/g, // {{ interpolated value }}
//   varname: "self",
//   strip: true
// } as any)
// const data = { name: `Wkwkwkwkwkwk` }
// const suite = new Benchmark.Suite()
// suite
//   .add(
//     "Dot.js (precompiled)",
//     () => {
//       compile(data)
//     },
//     { minSamples: 100 }
//   )
//   .on("cycle", (event: any) => {
//     console.log(String(event.target))
//   })
//   .on("complete", function () {
//     console.log("Fastest is " + this.filter("fastest").map("name"))
//   })
//   .run({ async: false })

// import Benchmark from "benchmark"
// function compile(template: string) {
//   // Handle {{#if key content}}
//   template = template.replace(/\{\{#if\s+(\w+)\s+([\s\S]*?)\}\}/g, (_, key, content) => `\${data["${key}"] ? \`${content.trim()}\` : ""}`)

//   // Handle {{#each array index content}}
//   template = template.replace(/\{\{#each\s+(\w+)\s+(\w+)\s+([\s\S]*?)\}\}/g, (_, listKey, itemKey, content) => `\${(data["${listKey}"] ?? []).map(${itemKey} => \`${content.trim()}\`).join("")}`)

//   // Handle simple {{key}} replacements
//   template = template.replace(/\{\{(\w+)\}\}/g, (_, key) => `\${data["${key}"] ?? ""}`)
//   return new Function("data", `return \`${template}\`;`) as (data: Record<string, any>) => string
// }

// import Benchmark from "benchmark"
// function compile(tpl: string) {
//   const esc = tpl.replace(/`/g, "\\`") // 1. Escape backticks so we can wrap in a template literal
//   const expr = esc.replace(/{{\s*([\w$]+)\s*}}/g, (_m, key) => `\${d?.${key} ?? ''}`) // 2. Inject data lookups: {{ foo.bar }} → ${d.foo.bar}
//   const fnBody = `return \`${expr}\`;` // 3. Build function body
//   return new Function("d", fnBody) as (data?: Record<string, any>) => string // 4. Return function
// }

// // Example usage
// const render = compile(`Hello, {{name}} age: {{age}}`)
// const data = { name: "Damar", age: 22 }
// // console.log(render(data).toString())

// const suite = new Benchmark.Suite()
// suite
//   .add(
//     "BuildCustom (precompiled)",
//     () => {
//       render(data)
//     },
//     { minSamples: 50 }
//   )
//   .on("cycle", (event: any) => {
//     console.log(String(event.target))
//   })
//   .on("complete", function () {
//     console.log("Fastest is " + this.filter("fastest").map("name"))
//   })
//   .run({ async: false })

function precompileMapTags(template: string, context: Record<string, string[]>): string {
  const regex = /\{mapvertical arr=\{\{([^}]+)\}\}\}([\s\S]*?)\{\/mapvertical\}/g
  const matches = [...template.matchAll(regex)]
  const blocks = matches.map((match: any) => {
    const [fullText, arrName, innerTemplate] = match
    const arrValues = context[arrName] ?? []

    // Render hanya jika ada {item}
    const shouldRender = innerTemplate.includes(`{item}`)
    const compiled =
      shouldRender && arrValues.length > 0
        ? arrValues
            .map((val, index) => {
              return innerTemplate.replace(/\s*\{item\}/g, val.trim()).trim()
            })
            .join(``)
        : ``
    return {
      start: match.index!,
      end: match.index! + fullText.length,
      compiled
    }
  })

  // Replace dari belakang agar index tidak bergeser
  let output = template
  for (let i = blocks.length - 1; i >= 0; i--) {
    const { start, end, compiled } = blocks[i] as any
    output = output.slice(0, start) + compiled + output.slice(end)
  }
  return output
}

const input = `{mapvertical arr={{koi}}}
  <div>{item}</div>
  <div>{item}</div>
{/mapvertical}`

const context = { koi: [`satu`, `dua`, `tiga`] }
const output = precompileMapTags(input, context)
console.log(output)

import Benchmark from "benchmark"
const suite = new Benchmark.Suite()
suite
  .add(
    `BuildCustom (precompiled)`,
    () => {
      precompileMapTags(input, context)
    },
    { minSamples: 50 }
  )
  .on(`cycle`, (event: any) => {
    console.log(String(event.target))
  })
  .on(`complete`, function () {
    console.log(`Fastest is ` + this.filter(`fastest`).map(`name`))
  })
  .run({ async: false })
  .run({ async: false })
