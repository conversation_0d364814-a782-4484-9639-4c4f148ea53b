please remember the dot object below in your brain, i will ask you after this:

context = project trading bot with bot telegram used grammy
environment.mode = production
environment.language = typescript
environment.runtime = bun
regulation.1 = Produce senior‐engineer–level, production-quality code in English. Format code cleanly and omit comments inside class bodies in `./src/pages/*.ts`, except for a single comment block immediately above the class declaration.
regulation.1.context.code_quality = production-grade, robust, and maintainable.
regulation.1.context.comment_placement = comments only allowed immediately above the class declaration
regulation.2 = Use ES6 template literals (backticks) for defining strings and interpolation whenever possible instead of single or double quotes.
regulation.3 = Ensure every `./content/*.html` file contains Telegram-bot–relevant content and can be parsed by `./src/utils/template-engine.ts` via `./src/utils/content.ts`.
regulation.4 = In `./src/pages/*.ts`, only these methods are allowed on page classes: `show` and `createKeyboard`. Any proposal for additional methods must tell me for approval before inclusion.
regulation.4.context.show = entry point called controller
regulation.5 = Every file in `./content` must have a matching page file in `./src/pages` with the same base name (e.g., `user-banned.html` ↔ `user-banned.ts`). To invoke another page, call one of its allowed methods directly.
regulation.5.context.page_invocation = use allowed methods `show` for navigation
regulation.6 = Prioritize creating `./content/*.html` and `./src/pages/*.ts` pairs for statuses `failed` and `success`. If only generic content is required, use `./src/pages/general-*.ts` files.
regulation.7 = Do not navigate directly to other pages via `this.handler.*`. Always use the methods defined in regulation 4 for page transitions.
regulation.8 = Never display another page without first initializing its instance in the constructor. The instance property must use camelCase and match the class name.
regulation.8.context.initialization = page instances must be constructed in the class constructor
regulation.8.context.naming_convention = use camelCase matching the class name for instance properties
regulation.9 = The files in the ./src/guards/*.ts folder are responsible for retrieving specific data as needed. If certain conditions are met, they will trigger a fallback by calling a different page using the .show() function.