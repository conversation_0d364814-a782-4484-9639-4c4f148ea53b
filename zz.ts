// Hai, saya akan mencoba merangkum semuanya secara detail dan membuat diagram yang baik.
// 1. <PERSON><PERSON><PERSON>, masukkan alamat token.
// 2. <PERSON><PERSON> layar terbuka, <PERSON>a dapat memilih jumlah investasi untuk setiap pembelian.
// 3. <PERSON><PERSON><PERSON>, Anda dapat menentukan persentase fluktuasi untuk order jual dan untuk setiap pembelian kembali, serta jumlah maksimum order jual yang tertunda.
// 4. Pilih opsi ini dari opsi yang telah ditentukan atau sesuaikan.
// 5. <PERSON><PERSON><PERSON>, pilih apakah akan menjual semuanya atau memulihkan investasi dan keuntungan. Pilih persentase penjualan untuk menghasilkan keuntungan dan persentase yang akan disimpan agar Anda dapat menjual secara manual kapan pun Anda mau.
// 6. Lokasi ini adalah tempat Anda dapat melihat semua yang dilakukan bot untuk setiap token yang dipilih.
// 7. Berapa banyak pembelian yang telah dilakukan dan berapa jumlahnya. Berapa banyak penjualan untuk jumlah berapa. Berapa banyak transaksi dalam persentase. Order jual. Dan berapa banyak token yang Anda miliki dan nilainya. Saya sudah berusaha sedetail mungkin. Beri tahu saya. Terima kasih.

// ==============================================================
// ==============================================================
// ==============================================================
//
// versi sudah di jelaskan
// 1. masukan alamat token
// 2. masukan jumlah investasi
// 3. masukan jumlah persen REBUY (berlaku untuk naik dan turun) dan persen SELL (berlaku untuk naik). keduanya mengacu pada price saat jumlah di investasikan (dinomor 2) dan setelah itu mengacu pada pembelian terakhir -1
// 4.

// import {} from "./src/blockchain/"

const run = () => {
  const inputTokenAddress = `...`
  const inputAmount = `1 SOL`
  const persentRebuy = `5%`
  const persentSell = `10%`
  const persentNow = `20%`
}

// trade -> list trade with button create config -> input (x?) -> done
// config -> list config with button create config -> input (x?) -> done

import { createId, getConstants } from "@paralleldrive/cuid2"
// setInterval(() => {
//   const id = createId()
//   if (id.length != 24) {
const id = `config-create-execute:${createId()}`
console.log(id.length, id)
//   }
// }, 1)
