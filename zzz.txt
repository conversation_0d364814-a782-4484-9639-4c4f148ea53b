biar saya jelaskan lebih detail tentang project ini, sekilas dulu project ini menggunakan DEX ethereum dan solana. di bawah adalah flow project ini : 
1. user untuk membuat wallet dulu 
2. buat config dengan wallet sebagai owner (wallet lain tidak bisa memakai config tersebut) 
3. user akan mengaktifkan config dari 0=inactive ke 1=active dan akan mengirim data nya ke thread lain 
4. thread lain menerima config user tersebut untuk system pengecekan harga token, lalu apa acuan nya?
   - di config terdapat rebuy percent, sell percent. 
   - step pertama: rebuy akan membeli jika harga token turun/naik sesuai persen yg ditetapkan, namun jika sudah membeli 1x misal di acuan turun 5% maka rebuy akan di setop dulu 
   - step kedua: jika salah sedangkan sell akan menjual nya saat harga naik lebih dari rebuy  